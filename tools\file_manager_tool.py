"""
File manager tool for reading, writing, and listing files.
"""

from pathlib import Path
from typing import Dict, Any, List

from tools.base_tool import BaseTool

class FileManagerTool(BaseTool):
    """A tool for managing files."""
    
    def execute(self, operation: str, path: str, content: str = None, **kwargs) -> Dict[str, Any]:
        """Execute a file operation.

        Args:
            operation (str): The operation to perform (read, write, list).
            path (str): The path to the file or directory.
            content (str, optional): The content to write to the file. Defaults to None.

        Returns:
            Dict[str, Any]: A dictionary containing the result of the operation.
        """
        try:
            p = Path(path)
            
            if operation == "read":
                return {"content": p.read_text()}
            
            elif operation == "write":
                p.write_text(content)
                return {"success": True}
            
            elif operation == "list":
                return {"files": [str(f) for f in p.iterdir()]}
            
            else:
                return {"error": f"Unsupported operation: {operation}"}
                
        except Exception as e:
            return {"error": str(e)}
