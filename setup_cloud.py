#!/usr/bin/env python3
"""
Cloud Setup Script for WebDev Agent
Configures cloud LLM providers (Gemini, Groq, OpenRouter) with automatic fallback.
"""

import sys
import subprocess
import os
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🤖 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a step description."""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing cloud LLM dependencies...")
    
    dependencies = [
        "google-generativeai>=0.3.2",
        "groq>=0.4.1", 
        "openai>=1.12.0",
        "anthropic>=0.18.0",
        "beautifulsoup4>=4.12.0",
        "selenium>=4.15.0",
        "requests>=2.31.0"
    ]
    
    for dep in dependencies:
        try:
            print(f"🔄 Installing {dep}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True, capture_output=True)
            print(f"✅ {dep} installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True

def setup_api_keys():
    """Setup API keys for cloud providers."""
    print("🔑 Setting up API keys...")
    
    api_keys = {}
    
    # Gemini (Google)
    print("\n🔵 Google Gemini Setup:")
    print("   Get your API key from: https://makersuite.google.com/app/apikey")
    gemini_key = input("   Enter Gemini API key (or press Enter to skip): ").strip()
    if gemini_key:
        api_keys['GEMINI_API_KEY'] = gemini_key
        print("   ✅ Gemini API key configured")
    
    # Groq
    print("\n🟢 Groq Setup:")
    print("   Get your API key from: https://console.groq.com/keys")
    groq_key = input("   Enter Groq API key (or press Enter to skip): ").strip()
    if groq_key:
        api_keys['GROQ_API_KEY'] = groq_key
        print("   ✅ Groq API key configured")
    
    # OpenRouter
    print("\n🟠 OpenRouter Setup:")
    print("   Get your API key from: https://openrouter.ai/keys")
    openrouter_key = input("   Enter OpenRouter API key (or press Enter to skip): ").strip()
    if openrouter_key:
        api_keys['OPENROUTER_API_KEY'] = openrouter_key
        print("   ✅ OpenRouter API key configured")
    
    # OpenAI (backup)
    print("\n🔴 OpenAI Setup (Optional Backup):")
    print("   Get your API key from: https://platform.openai.com/api-keys")
    openai_key = input("   Enter OpenAI API key (or press Enter to skip): ").strip()
    if openai_key:
        api_keys['OPENAI_API_KEY'] = openai_key
        print("   ✅ OpenAI API key configured")
    
    if not api_keys:
        print("⚠️  No API keys configured. You'll need at least one to use the agent.")
        return False
    
    # Save to .env file
    env_file = Path(".env")
    with open(env_file, "w") as f:
        f.write("# WebDev Agent Cloud API Configuration\n")
        f.write("# Generated by setup_cloud.py\n\n")
        
        for key, value in api_keys.items():
            f.write(f"{key}={value}\n")
        
        f.write("\n# Primary provider (gemini, groq, openrouter)\n")
        f.write("WEBDEV_PRIMARY_PROVIDER=gemini\n")
    
    print(f"✅ API keys saved to {env_file}")
    return True

def test_api_connections():
    """Test connections to all configured APIs."""
    print("🧪 Testing API connections...")
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Import the cloud LLM manager
        sys.path.insert(0, str(Path.cwd()))
        from utils.cloud_llm_manager import CloudLLMManager
        
        # Initialize and test
        llm_manager = CloudLLMManager()
        
        print(f"   Available providers: {llm_manager.available_providers}")
        
        if llm_manager.available_providers:
            # Test with a simple prompt
            try:
                response = llm_manager.generate("Hello, respond with just 'OK'", max_tokens=10)
                if response and "ok" in response.lower():
                    print("✅ LLM connection test successful")
                    return True
                else:
                    print(f"⚠️  Unexpected response: {response}")
                    return False
            except Exception as e:
                print(f"❌ LLM test failed: {e}")
                return False
        else:
            print("❌ No LLM providers available")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Try: pip install python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def create_example_config():
    """Create an example configuration file."""
    print("📝 Creating example configuration...")
    
    config_example = {
        "llm": {
            "primary_provider": "gemini",
            "fallback_providers": ["groq", "openrouter", "openai"],
            "gemini_model": "gemini-1.5-flash",
            "groq_model": "llama-3.1-70b-versatile", 
            "openrouter_model": "meta-llama/llama-3.1-8b-instruct:free",
            "temperature": 0.1,
            "max_tokens": 4096
        },
        "project": {
            "backend_framework": "flask",
            "database": "sqlite",
            "frontend_approach": "jinja2"
        }
    }
    
    config_file = Path("config_example.json")
    with open(config_file, "w") as f:
        json.dump(config_example, f, indent=2)
    
    print(f"✅ Example configuration saved to {config_file}")
    return True

def setup_web_dev_tools():
    """Setup additional web development tools."""
    print("🛠️  Setting up web development tools...")
    
    # Install additional tools
    tools = [
        "black",  # Code formatter
        "flake8",  # Linter
        "pylint",  # Code analysis
        "mypy",  # Type checker
        "bandit",  # Security scanner
        "safety",  # Dependency scanner
    ]
    
    for tool in tools:
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", tool
            ], check=True, capture_output=True)
            print(f"✅ {tool} installed")
        except subprocess.CalledProcessError:
            print(f"⚠️  {tool} installation failed (optional)")
    
    return True

def show_completion_message():
    """Show completion message and next steps."""
    print_header("Setup Complete! 🎉")
    
    print("🚀 WebDev Agent is ready with cloud LLM providers!")
    
    print("\n🔑 Configured Providers:")
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, "r") as f:
            content = f.read()
            if "GEMINI_API_KEY" in content:
                print("   ✅ Google Gemini")
            if "GROQ_API_KEY" in content:
                print("   ✅ Groq")
            if "OPENROUTER_API_KEY" in content:
                print("   ✅ OpenRouter")
            if "OPENAI_API_KEY" in content:
                print("   ✅ OpenAI (backup)")
    
    print("\n📖 Quick Start:")
    print("   1. 🌐 Web Interface:")
    print("      python app.py web")
    print("      Open: http://localhost:5555")
    
    print("\n   2. 💻 Command Line:")
    print("      python app.py 'Create a blog application'")
    
    print("\n   3. 🧪 Test Setup:")
    print("      python test_cloud_setup.py")
    
    print("\n🔧 Configuration:")
    print("   • API keys: .env file")
    print("   • Settings: config.py")
    print("   • Example: config_example.json")
    
    print("\n💡 Tips:")
    print("   • Gemini is free and fast for most tasks")
    print("   • Groq offers very fast inference")
    print("   • OpenRouter has many free models")
    print("   • The system automatically falls back if one provider fails")

def main():
    """Main setup process."""
    print_header("WebDev Agent Cloud Setup")
    print("🎯 This script will configure cloud LLM providers for WebDev Agent")
    print("⏱️  Estimated time: 5-10 minutes")
    
    # Confirm before proceeding
    try:
        response = input("\nProceed with cloud setup? (Y/n): ").strip().lower()
        if response in ['n', 'no']:
            print("👋 Setup cancelled")
            return
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        return
    
    # Run setup steps
    steps = [
        ("Check Python Version", check_python_version),
        ("Install Dependencies", install_dependencies),
        ("Setup API Keys", setup_api_keys),
        ("Test Connections", test_api_connections),
        ("Create Example Config", create_example_config),
        ("Setup Dev Tools", setup_web_dev_tools)
    ]
    
    failed_steps = []
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print_step(i, step_name)
        try:
            if not step_func():
                failed_steps.append(step_name)
                if step_name == "Setup API Keys":
                    print("⚠️  Cannot continue without API keys")
                    break
        except KeyboardInterrupt:
            print("\n👋 Setup cancelled by user")
            return
        except Exception as e:
            print(f"❌ Step failed with error: {e}")
            failed_steps.append(step_name)
    
    # Show results
    if not failed_steps:
        show_completion_message()
    else:
        print_header("Setup Completed with Issues")
        print("⚠️  Some steps failed:")
        for step in failed_steps:
            print(f"   • {step}")
        
        print("\n💡 You can re-run this setup script to fix issues.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Setup failed with unexpected error: {e}")
        sys.exit(1)
