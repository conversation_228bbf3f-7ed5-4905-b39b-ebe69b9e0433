"""
Terminal tool for executing shell commands.
"""

import subprocess
from typing import Dict, Any

from tools.base_tool import BaseTool

class TerminalTool(BaseTool):
    """A tool for executing shell commands."""
    
    def execute(self, command: str, **kwargs) -> Dict[str, Any]:
        """Execute a shell command.

        Args:
            command (str): The command to execute.

        Returns:
            Dict[str, Any]: A dictionary containing the result of the command.
        """
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            return {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
            
        except Exception as e:
            return {
                "error": str(e)
            }
