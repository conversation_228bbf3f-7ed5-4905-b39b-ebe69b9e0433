"""
Planner Agent for breaking down web development requests into actionable tasks.
Analyzes requirements and creates detailed task plans for other agents to execute.
"""

import json
import uuid
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .base_agent import BaseAgent, AgentTask, AgentResponse
from memory.task_memory import TaskMemory
from utils.llm_manager import LL<PERSON>anager
from utils.project_analyzer import <PERSON><PERSON><PERSON>yzer

@dataclass
class TaskPlan:
    """Represents a complete task plan for a project."""
    id: str
    description: str
    tasks: List[AgentTask]
    dependencies: Dict[str, List[str]]  # task_id -> [dependency_task_ids]
    estimated_duration: int  # in minutes
    priority: int
    project_type: str
    framework: str
    features: List[str]

class PlannerAgent(BaseAgent):
    """Agent responsible for planning and task breakdown."""
    
    def __init__(self, memory: TaskMemory, llm_manager: LLMManager):
        super().__init__("Planner", "Task Planning and Project Analysis", memory, llm_manager)
        self.project_analyzer = ProjectAnalyzer()
        self.task_templates = self._load_task_templates()
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the planner agent."""
        return """You are a Senior Software Architect and Project Planner specializing in Python web development.

Your role is to:
1. Analyze project requirements and break them down into specific, actionable tasks
2. Create detailed task plans with proper dependencies and sequencing
3. Estimate effort and identify potential risks
4. Recommend appropriate frameworks, tools, and project structure
5. Ensure tasks are specific enough for other agents to execute

When creating task plans:
- Break down complex features into smaller, manageable tasks
- Identify dependencies between tasks
- Specify which agent should handle each task (Developer, Tester, Terminal)
- Include specific file names, function names, and implementation details
- Consider testing requirements for each feature
- Plan for proper error handling and validation

Focus on Python-based solutions using Flask/FastAPI, Jinja2 templates, and SQLite/PostgreSQL databases.
Prioritize clean, maintainable code structure and comprehensive testing.

Always provide detailed, actionable task descriptions that other agents can execute without ambiguity."""
    
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        return [
            "plan_project",
            "analyze_requirements", 
            "break_down_feature",
            "estimate_effort",
            "create_task_sequence",
            "analyze_existing_project",
            "recommend_architecture"
        ]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a planning task."""
        if task.type == "plan_project":
            return self._plan_project(task)
        elif task.type == "analyze_requirements":
            return self._analyze_requirements(task)
        elif task.type == "break_down_feature":
            return self._break_down_feature(task)
        elif task.type == "analyze_existing_project":
            return self._analyze_existing_project(task)
        elif task.type == "recommend_architecture":
            return self._recommend_architecture(task)
        else:
            return AgentResponse(
                success=False,
                error=f"Unsupported task type: {task.type}"
            )
    
    def _plan_project(self, task: AgentTask) -> AgentResponse:
        """Create a complete project plan from requirements."""
        try:
            requirements = task.description
            
            # Generate planning prompt
            prompt = f"""
            Create a detailed project plan for the following web application:
            
            Requirements: {requirements}
            
            Please provide:
            1. Project overview and recommended architecture
            2. Framework recommendation (Flask or FastAPI)
            3. Database choice and schema design
            4. Detailed task breakdown with specific file names
            5. Task dependencies and execution order
            6. Testing strategy
            7. Estimated timeline
            
            Format the response as JSON with the following structure:
            {{
                "project_overview": "...",
                "framework": "flask|fastapi",
                "database": "sqlite|postgresql",
                "architecture": "monolithic|modular|blueprints",
                "features": ["feature1", "feature2", ...],
                "tasks": [
                    {{
                        "id": "task_1",
                        "type": "create_file|modify_file|run_command|test",
                        "agent": "Developer|Tester|Terminal",
                        "description": "Detailed task description",
                        "files": ["file1.py", "file2.html"],
                        "priority": 1-5,
                        "estimated_minutes": 30,
                        "dependencies": ["task_id1", "task_id2"]
                    }}
                ],
                "timeline": "X hours/days",
                "risks": ["risk1", "risk2"]
            }}
            """
            
            response = self.generate_response(prompt)
            
            # Parse the JSON response
            try:
                plan_data = json.loads(response)
                task_plan = self._create_task_plan(plan_data, requirements)
                
                return AgentResponse(
                    success=True,
                    result={
                        "task_plan": task_plan,
                        "plan_data": plan_data
                    },
                    next_tasks=task_plan.tasks
                )
                
            except json.JSONDecodeError:
                # Fallback: create a basic plan
                return self._create_fallback_plan(requirements)
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating project plan: {str(e)}"
            )
    
    def _analyze_requirements(self, task: AgentTask) -> AgentResponse:
        """Analyze and clarify project requirements."""
        try:
            requirements = task.description
            
            prompt = f"""
            Analyze the following project requirements and provide:
            1. Clarified and detailed requirements
            2. Identified ambiguities or missing information
            3. Suggested additional features
            4. Technical constraints and considerations
            
            Requirements: {requirements}
            
            Provide a structured analysis in JSON format.
            """
            
            response = self.generate_response(prompt)
            
            return AgentResponse(
                success=True,
                result={"analysis": response}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error analyzing requirements: {str(e)}"
            )
    
    def _break_down_feature(self, task: AgentTask) -> AgentResponse:
        """Break down a specific feature into implementation tasks."""
        try:
            feature_description = task.description
            
            prompt = f"""
            Break down the following feature into specific implementation tasks:
            
            Feature: {feature_description}
            
            Create detailed tasks for:
            1. Backend implementation (models, routes, business logic)
            2. Frontend implementation (templates, forms, JavaScript)
            3. Database changes (migrations, schema updates)
            4. Testing (unit tests, integration tests)
            5. Documentation updates
            
            Each task should specify:
            - Exact files to create/modify
            - Function/class names to implement
            - Dependencies on other tasks
            - Estimated effort
            
            Format as JSON with task details.
            """
            
            response = self.generate_response(prompt)
            
            return AgentResponse(
                success=True,
                result={"feature_breakdown": response}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error breaking down feature: {str(e)}"
            )
    
    def _analyze_existing_project(self, task: AgentTask) -> AgentResponse:
        """Analyze an existing project and suggest improvements."""
        try:
            project_path = task.description or "."
            
            # Use project analyzer
            self.project_analyzer = ProjectAnalyzer(project_path)
            analysis = self.project_analyzer.analyze_project()
            
            # Generate improvement suggestions
            prompt = f"""
            Based on the following project analysis, suggest improvements and next development tasks:
            
            Project Analysis:
            - Framework: {analysis.framework}
            - Structure: {analysis.structure_type}
            - Database: {analysis.database}
            - Total files: {len(analysis.files)}
            - Total lines: {analysis.total_lines}
            - Complexity score: {analysis.complexity_score}
            - Recommendations: {analysis.recommendations}
            
            File breakdown:
            {json.dumps([{"path": f.path, "type": f.type, "lines": f.lines} for f in analysis.files[:10]], indent=2)}
            
            Provide:
            1. Code quality assessment
            2. Architecture improvement suggestions
            3. Specific refactoring tasks
            4. Feature enhancement opportunities
            5. Testing gaps to address
            
            Format as actionable tasks with priorities.
            """
            
            response = self.generate_response(prompt)
            
            return AgentResponse(
                success=True,
                result={
                    "project_analysis": analysis,
                    "improvement_suggestions": response
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error analyzing existing project: {str(e)}"
            )
    
    def _recommend_architecture(self, task: AgentTask) -> AgentResponse:
        """Recommend architecture for a project based on requirements."""
        try:
            requirements = task.description
            
            prompt = f"""
            Recommend the best architecture for a web application with these requirements:
            
            {requirements}
            
            Consider:
            1. Framework choice (Flask vs FastAPI)
            2. Project structure (monolithic, modular, blueprints)
            3. Database choice and design
            4. Authentication and authorization approach
            5. API design (REST, GraphQL)
            6. Frontend approach (server-side rendering, SPA, hybrid)
            7. Deployment considerations
            8. Scalability requirements
            
            Provide detailed recommendations with justifications.
            """
            
            response = self.generate_response(prompt)
            
            return AgentResponse(
                success=True,
                result={"architecture_recommendations": response}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error recommending architecture: {str(e)}"
            )
    
    def _create_task_plan(self, plan_data: Dict[str, Any], requirements: str) -> TaskPlan:
        """Create a TaskPlan object from plan data."""
        plan_id = str(uuid.uuid4())
        
        # Convert task data to AgentTask objects
        tasks = []
        for task_data in plan_data.get("tasks", []):
            agent_task = AgentTask(
                id=task_data.get("id", str(uuid.uuid4())),
                type=task_data.get("type", "unknown"),
                description=task_data.get("description", ""),
                priority=task_data.get("priority", 1),
                dependencies=task_data.get("dependencies", [])
            )
            tasks.append(agent_task)
        
        # Create dependencies mapping
        dependencies = {}
        for task in tasks:
            dependencies[task.id] = task.dependencies
        
        return TaskPlan(
            id=plan_id,
            description=requirements,
            tasks=tasks,
            dependencies=dependencies,
            estimated_duration=sum(task_data.get("estimated_minutes", 30) for task_data in plan_data.get("tasks", [])),
            priority=1,
            project_type=plan_data.get("architecture", "unknown"),
            framework=plan_data.get("framework", "flask"),
            features=plan_data.get("features", [])
        )
    
    def _create_fallback_plan(self, requirements: str) -> AgentResponse:
        """Create a basic fallback plan when LLM parsing fails."""
        tasks = [
            AgentTask(
                id="setup_project",
                type="create_project_structure",
                description=f"Create basic project structure for: {requirements}",
                priority=1
            ),
            AgentTask(
                id="create_app",
                type="create_main_app",
                description="Create main application file with basic Flask setup",
                priority=2,
                dependencies=["setup_project"]
            ),
            AgentTask(
                id="create_routes",
                type="create_routes",
                description="Create basic routes and views",
                priority=3,
                dependencies=["create_app"]
            ),
            AgentTask(
                id="create_templates",
                type="create_templates",
                description="Create HTML templates",
                priority=4,
                dependencies=["create_routes"]
            ),
            AgentTask(
                id="create_tests",
                type="create_tests",
                description="Create basic test suite",
                priority=5,
                dependencies=["create_templates"]
            )
        ]
        
        task_plan = TaskPlan(
            id=str(uuid.uuid4()),
            description=requirements,
            tasks=tasks,
            dependencies={task.id: task.dependencies for task in tasks},
            estimated_duration=120,  # 2 hours
            priority=1,
            project_type="monolithic",
            framework="flask",
            features=["basic_web_app"]
        )
        
        return AgentResponse(
            success=True,
            result={"task_plan": task_plan},
            next_tasks=tasks
        )
    
    def _load_task_templates(self) -> Dict[str, Any]:
        """Load task templates for common project types."""
        return {
            "flask_basic": {
                "files": ["app.py", "routes.py", "models.py", "templates/base.html"],
                "tasks": ["setup", "create_app", "create_routes", "create_models", "create_templates"]
            },
            "fastapi_basic": {
                "files": ["main.py", "routers/", "models.py", "database.py"],
                "tasks": ["setup", "create_main", "create_routers", "create_models", "create_database"]
            }
        }
