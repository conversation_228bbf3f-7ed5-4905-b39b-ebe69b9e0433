"""
WebDev Agent - Main Orchestrator
Coordinates multiple specialized agents to build web applications autonomously.
"""

import uuid
import time
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from config import get_config
from memory.task_memory import TaskMemory
from memory.project_memory import ProjectMemory, ProjectState
from utils.llm_manager import LL<PERSON>anager
from utils.file_manager import FileManager
from utils.project_analyzer import ProjectAnalyzer
from agents.planner_agent import PlannerAgent
from agents.developer_agent import DeveloperAgent
from agents.tester_agent import TesterAgent
from agents.terminal_agent import TerminalAgent
from agents.base_agent import AgentTask, AgentResponse

class WebDevAgent:
    """Main orchestrator for the WebDev Agent system."""
    
    def __init__(self, project_path: str = "."):
        self.project_path = Path(project_path).resolve()
        self.config = get_config()
        
        # Initialize core systems
        self.task_memory = TaskMemory()
        self.project_memory = ProjectMemory()
        self.llm_manager = LLMManager()
        self.file_manager = FileManager(str(self.project_path))
        self.project_analyzer = ProjectAnalyzer(str(self.project_path))
        
        # Initialize agents
        self.agents = {
            "planner": PlannerAgent(self.task_memory, self.llm_manager),
            "developer": DeveloperAgent(self.task_memory, self.llm_manager, str(self.project_path)),
            "tester": TesterAgent(self.task_memory, self.llm_manager, str(self.project_path)),
            "terminal": TerminalAgent(self.task_memory, self.llm_manager, str(self.project_path))
        }
        
        # Current project state
        self.current_project: Optional[ProjectState] = None
        self.session_id: Optional[str] = None
        
        # Task execution state
        self.task_queue: List[AgentTask] = []
        self.completed_tasks: List[AgentTask] = []
        self.failed_tasks: List[AgentTask] = []
    
    def start_session(self, project_description: str = None) -> str:
        """Start a new development session."""
        self.session_id = str(uuid.uuid4())
        
        # Check if this is an existing project
        existing_project = self.project_memory.get_project_by_path(str(self.project_path))
        
        if existing_project:
            print(f"Resuming existing project: {existing_project.name}")
            self.current_project = existing_project
            self._analyze_existing_project()
        else:
            print("Starting new project...")
            self.current_project = self._create_new_project(project_description)
        
        # Start session in memory
        if self.current_project:
            self.project_memory.start_session(self.session_id, self.current_project.project_id)
            print(f"Session started: {self.session_id}")
        
        return self.session_id
    
    def end_session(self, achievements: List[str] = None, issues: List[str] = None, notes: str = None):
        """End the current development session."""
        if self.session_id and self.current_project:
            self.project_memory.end_session(
                self.session_id, 
                achievements or [],
                issues or [],
                notes
            )
            
            # Update project state
            self.current_project.files_created.extend(self.file_manager.get_created_files())
            self.current_project.files_modified.extend(self.file_manager.get_modified_files())
            self.project_memory.update_project(self.current_project)
            
            print(f"Session ended: {self.session_id}")
            self.session_id = None
    
    def build_application(self, requirements: str) -> Dict[str, Any]:
        """Build a complete web application from requirements."""
        print(f"Building application: {requirements}")
        
        try:
            # Start session
            session_id = self.start_session(requirements)
            
            # Step 1: Plan the project
            print("Step 1: Planning project...")
            plan_task = AgentTask(
                id=str(uuid.uuid4()),
                type="plan_project",
                description=requirements,
                priority=1
            )
            
            plan_response = self.agents["planner"].run_task(plan_task)
            
            if not plan_response.success:
                return {
                    "success": False,
                    "error": f"Planning failed: {plan_response.error}",
                    "session_id": session_id
                }
            
            # Extract task plan
            task_plan = plan_response.result.get("task_plan")
            if task_plan:
                self.task_queue.extend(task_plan.tasks)
                print(f"Created {len(task_plan.tasks)} tasks")
            
            # Step 2: Execute tasks
            print("Step 2: Executing tasks...")
            execution_results = self._execute_task_queue()
            
            # Step 3: Run tests
            print("Step 3: Running tests...")
            test_results = self._run_tests()
            
            # Step 4: Generate summary
            summary = self._generate_project_summary()
            
            # End session
            achievements = [
                f"Created {len(self.file_manager.get_created_files())} files",
                f"Completed {len(self.completed_tasks)} tasks"
            ]
            
            issues = [task.error for task in self.failed_tasks if task.error]
            
            self.end_session(achievements, issues, "Project build completed")
            
            return {
                "success": True,
                "session_id": session_id,
                "project_id": self.current_project.project_id if self.current_project else None,
                "execution_results": execution_results,
                "test_results": test_results,
                "summary": summary,
                "files_created": self.file_manager.get_created_files(),
                "files_modified": self.file_manager.get_modified_files()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Build failed: {str(e)}",
                "session_id": session_id if 'session_id' in locals() else None
            }
    
    def continue_project(self, additional_requirements: str = None) -> Dict[str, Any]:
        """Continue development on an existing project."""
        if not self.current_project:
            return {
                "success": False,
                "error": "No current project to continue"
            }
        
        print(f"Continuing project: {self.current_project.name}")
        
        # Analyze current state
        self._analyze_existing_project()
        
        if additional_requirements:
            # Plan additional features
            plan_task = AgentTask(
                id=str(uuid.uuid4()),
                type="break_down_feature",
                description=additional_requirements,
                priority=1
            )
            
            plan_response = self.agents["planner"].run_task(plan_task)
            
            if plan_response.success:
                # Add new tasks to queue
                # This would need to parse the response and create tasks
                print("Added new tasks for additional requirements")
        
        # Execute pending tasks
        execution_results = self._execute_task_queue()
        
        return {
            "success": True,
            "project_id": self.current_project.project_id,
            "execution_results": execution_results
        }
    
    def _create_new_project(self, description: str) -> ProjectState:
        """Create a new project state."""
        project_id = str(uuid.uuid4())
        
        project_state = ProjectState(
            project_id=project_id,
            name=f"WebApp_{int(time.time())}",
            path=str(self.project_path),
            framework="flask",  # Default, will be updated by planner
            database="sqlite",  # Default, will be updated by planner
            structure_type="unknown",
            created_at=time.time(),
            last_modified=time.time(),
            status="active",
            description=description or "Web application",
            features=[],
            files_created=[],
            files_modified=[],
            dependencies={},
            configuration={},
            metadata={}
        )
        
        self.project_memory.create_project(project_state)
        return project_state
    
    def _analyze_existing_project(self):
        """Analyze the existing project structure."""
        print("Analyzing existing project...")
        
        try:
            analysis = self.project_analyzer.analyze_project()
            
            if self.current_project:
                # Update project state with analysis results
                self.current_project.framework = analysis.framework
                self.current_project.database = analysis.database
                self.current_project.structure_type = analysis.structure_type
                self.current_project.dependencies = analysis.dependencies
                
                self.project_memory.update_project(self.current_project)
                
                print(f"Project analysis complete:")
                print(f"  Framework: {analysis.framework}")
                print(f"  Database: {analysis.database}")
                print(f"  Structure: {analysis.structure_type}")
                print(f"  Files: {len(analysis.files)}")
                print(f"  Total lines: {analysis.total_lines}")
                
        except Exception as e:
            print(f"Error analyzing project: {e}")
    
    def _execute_task_queue(self) -> Dict[str, Any]:
        """Execute all tasks in the queue."""
        results = {
            "total_tasks": len(self.task_queue),
            "completed": 0,
            "failed": 0,
            "task_results": []
        }
        
        while self.task_queue:
            task = self.task_queue.pop(0)
            
            print(f"Executing task: {task.type} - {task.description[:50]}...")
            
            # Determine which agent should handle this task
            agent = self._get_agent_for_task(task)
            
            if not agent:
                print(f"No agent available for task type: {task.type}")
                task.status = "failed"
                task.error = f"No agent available for task type: {task.type}"
                self.failed_tasks.append(task)
                results["failed"] += 1
                continue
            
            # Execute the task
            response = agent.run_task(task)
            
            # Record the result
            task_result = {
                "task_id": task.id,
                "task_type": task.type,
                "agent": agent.name,
                "success": response.success,
                "error": response.error,
                "files_created": response.files_created,
                "files_modified": response.files_modified
            }
            
            results["task_results"].append(task_result)
            
            if response.success:
                self.completed_tasks.append(task)
                results["completed"] += 1
                
                # Add any follow-up tasks
                if response.next_tasks:
                    self.task_queue.extend(response.next_tasks)
                    results["total_tasks"] += len(response.next_tasks)
                
            else:
                self.failed_tasks.append(task)
                results["failed"] += 1
                print(f"Task failed: {response.error}")
        
        return results
    
    def _get_agent_for_task(self, task: AgentTask):
        """Determine which agent should handle a task."""
        for agent in self.agents.values():
            if agent.can_handle_task(task):
                return agent
        return None
    
    def _run_tests(self) -> Dict[str, Any]:
        """Run the test suite."""
        print("Running tests...")
        
        test_task = AgentTask(
            id=str(uuid.uuid4()),
            type="run_tests",
            description="tests/",
            priority=1
        )
        
        response = self.agents["tester"].run_task(test_task)
        
        return {
            "success": response.success,
            "results": response.result,
            "error": response.error
        }
    
    def _generate_project_summary(self) -> Dict[str, Any]:
        """Generate a summary of the project."""
        if not self.current_project:
            return {}
        
        return {
            "project_name": self.current_project.name,
            "framework": self.current_project.framework,
            "database": self.current_project.database,
            "structure": self.current_project.structure_type,
            "files_created": len(self.current_project.files_created),
            "files_modified": len(self.current_project.files_modified),
            "tasks_completed": len(self.completed_tasks),
            "tasks_failed": len(self.failed_tasks),
            "features": self.current_project.features,
            "dependencies": self.current_project.dependencies
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current system status."""
        return {
            "session_active": self.session_id is not None,
            "session_id": self.session_id,
            "current_project": self.current_project.project_id if self.current_project else None,
            "tasks_in_queue": len(self.task_queue),
            "tasks_completed": len(self.completed_tasks),
            "tasks_failed": len(self.failed_tasks),
            "agents_status": {name: agent.get_status() for name, agent in self.agents.items()},
            "llm_status": self.llm_manager.get_status()
        }
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """List all projects."""
        projects = self.project_memory.list_projects()
        return [
            {
                "project_id": p.project_id,
                "name": p.name,
                "path": p.path,
                "framework": p.framework,
                "status": p.status,
                "created_at": p.created_at,
                "last_modified": p.last_modified
            }
            for p in projects
        ]

def main():
    """Main entry point for the WebDev Agent."""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python main.py <project_description>")
        print("Example: python main.py 'Create a blog application with user authentication'")
        return
    
    project_description = " ".join(sys.argv[1:])
    
    # Initialize the agent
    agent = WebDevAgent()
    
    # Build the application
    result = agent.build_application(project_description)
    
    # Print results
    if result["success"]:
        print("\n✅ Application built successfully!")
        print(f"Session ID: {result['session_id']}")
        print(f"Files created: {len(result['files_created'])}")
        print(f"Files modified: {len(result['files_modified'])}")
        
        if result['files_created']:
            print("\nFiles created:")
            for file_path in result['files_created']:
                print(f"  - {file_path}")
        
        print(f"\nProject summary:")
        summary = result['summary']
        for key, value in summary.items():
            print(f"  {key}: {value}")
            
    else:
        print(f"\n❌ Application build failed: {result['error']}")
        if result.get('session_id'):
            print(f"Session ID: {result['session_id']}")

if __name__ == "__main__":
    main()
