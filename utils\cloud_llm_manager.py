"""
Cloud LLM Manager for handling multiple cloud-based language models with automatic fallback.
Supports Gemini, Groq, OpenRouter, OpenAI, and Anthropic APIs.
"""

import json
import time
import requests
import asyncio
from typing import Dict, Any, Optional, List
import logging

from config import get_config, LLMConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CloudLLMManager:
    """Manages multiple cloud LLM providers with automatic fallback."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        self.config = config or get_config().llm
        self.available_providers = []
        self.failed_providers = set()
        
        # Initialize all providers
        self._init_providers()
    
    def _init_providers(self) -> None:
        """Initialize all available providers."""
        providers = {
            'gemini': self._init_gemini,
            'groq': self._init_groq,
            'openrouter': self._init_openrouter,
            'openai': self._init_openai,
            'anthropic': self._init_anthropic
        }
        
        for provider_name, init_func in providers.items():
            try:
                if init_func():
                    self.available_providers.append(provider_name)
                    logger.info(f"✅ {provider_name.title()} provider initialized")
                else:
                    logger.warning(f"⚠️  {provider_name.title()} provider not available")
            except Exception as e:
                logger.error(f"❌ {provider_name.title()} initialization failed: {e}")
        
        if not self.available_providers:
            logger.error("❌ No LLM providers available!")
        else:
            logger.info(f"🚀 Available providers: {', '.join(self.available_providers)}")
    
    def _init_gemini(self) -> bool:
        """Initialize Google Gemini provider."""
        if not self.config.gemini_api_key:
            return False
        
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.config.gemini_api_key)
            
            # Test with a simple request
            model = genai.GenerativeModel(self.config.gemini_model)
            response = model.generate_content("Hello", 
                generation_config=genai.types.GenerationConfig(max_output_tokens=1))
            
            return bool(response.text)
        except Exception as e:
            logger.debug(f"Gemini test failed: {e}")
            return False
    
    def _init_groq(self) -> bool:
        """Initialize Groq provider."""
        if not self.config.groq_api_key:
            return False
        
        try:
            from groq import Groq
            client = Groq(api_key=self.config.groq_api_key)
            
            # Test with a simple request
            response = client.chat.completions.create(
                model=self.config.groq_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )
            
            return bool(response.choices[0].message.content)
        except Exception as e:
            logger.debug(f"Groq test failed: {e}")
            return False
    
    def _init_openrouter(self) -> bool:
        """Initialize OpenRouter provider."""
        if not self.config.openrouter_api_key:
            return False
        
        try:
            headers = {
                "Authorization": f"Bearer {self.config.openrouter_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.openrouter_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 1
            }
            
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=10
            )
            
            return response.status_code == 200
        except Exception as e:
            logger.debug(f"OpenRouter test failed: {e}")
            return False
    
    def _init_openai(self) -> bool:
        """Initialize OpenAI provider."""
        if not self.config.openai_api_key:
            return False
        
        try:
            import openai
            client = openai.OpenAI(api_key=self.config.openai_api_key)
            
            # Test with a simple request
            response = client.chat.completions.create(
                model=self.config.openai_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )
            
            return bool(response.choices[0].message.content)
        except Exception as e:
            logger.debug(f"OpenAI test failed: {e}")
            return False
    
    def _init_anthropic(self) -> bool:
        """Initialize Anthropic provider."""
        if not self.config.anthropic_api_key:
            return False
        
        try:
            import anthropic
            client = anthropic.Anthropic(api_key=self.config.anthropic_api_key)
            
            # Test with a simple request
            response = client.messages.create(
                model=self.config.anthropic_model,
                max_tokens=1,
                messages=[{"role": "user", "content": "Hello"}]
            )
            
            return bool(response.content[0].text)
        except Exception as e:
            logger.debug(f"Anthropic test failed: {e}")
            return False
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using available providers with fallback."""
        # Determine provider order
        providers_to_try = [self.config.primary_provider]
        providers_to_try.extend([p for p in self.config.fallback_providers 
                               if p != self.config.primary_provider])
        
        # Filter to only available providers
        providers_to_try = [p for p in providers_to_try 
                          if p in self.available_providers and p not in self.failed_providers]
        
        if not providers_to_try:
            # Reset failed providers and try again
            self.failed_providers.clear()
            providers_to_try = [p for p in self.available_providers]
        
        last_error = None
        
        for provider in providers_to_try:
            try:
                logger.info(f"🔄 Trying {provider} provider...")
                result = self._generate_with_provider(provider, prompt, **kwargs)
                
                if result:
                    logger.info(f"✅ Success with {provider}")
                    # Remove from failed providers if it was there
                    self.failed_providers.discard(provider)
                    return result
                
            except Exception as e:
                logger.warning(f"❌ {provider} failed: {e}")
                self.failed_providers.add(provider)
                last_error = e
                continue
        
        # All providers failed
        raise Exception(f"All LLM providers failed. Last error: {last_error}")
    
    def _generate_with_provider(self, provider: str, prompt: str, **kwargs) -> str:
        """Generate text with a specific provider."""
        if provider == "gemini":
            return self._generate_gemini(prompt, **kwargs)
        elif provider == "groq":
            return self._generate_groq(prompt, **kwargs)
        elif provider == "openrouter":
            return self._generate_openrouter(prompt, **kwargs)
        elif provider == "openai":
            return self._generate_openai(prompt, **kwargs)
        elif provider == "anthropic":
            return self._generate_anthropic(prompt, **kwargs)
        else:
            raise ValueError(f"Unknown provider: {provider}")
    
    def _generate_gemini(self, prompt: str, **kwargs) -> str:
        """Generate text using Google Gemini."""
        import google.generativeai as genai
        
        model = genai.GenerativeModel(self.config.gemini_model)
        
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=kwargs.get("temperature", self.config.temperature),
                max_output_tokens=kwargs.get("max_tokens", self.config.max_tokens),
            )
        )
        
        return response.text
    
    def _generate_groq(self, prompt: str, **kwargs) -> str:
        """Generate text using Groq."""
        from groq import Groq
        
        client = Groq(api_key=self.config.groq_api_key)
        
        response = client.chat.completions.create(
            model=self.config.groq_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=kwargs.get("temperature", self.config.temperature),
            max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
        )
        
        return response.choices[0].message.content
    
    def _generate_openrouter(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenRouter."""
        headers = {
            "Authorization": f"Bearer {self.config.openrouter_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.openrouter_model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
        }
        
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=self.config.timeout
        )
        
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]
    
    def _generate_openai(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenAI."""
        import openai
        
        client = openai.OpenAI(api_key=self.config.openai_api_key)
        
        response = client.chat.completions.create(
            model=self.config.openai_model,
            messages=[{"role": "user", "content": prompt}],
            temperature=kwargs.get("temperature", self.config.temperature),
            max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
        )
        
        return response.choices[0].message.content
    
    def _generate_anthropic(self, prompt: str, **kwargs) -> str:
        """Generate text using Anthropic."""
        import anthropic
        
        client = anthropic.Anthropic(api_key=self.config.anthropic_api_key)
        
        response = client.messages.create(
            model=self.config.anthropic_model,
            max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
            temperature=kwargs.get("temperature", self.config.temperature),
            messages=[{"role": "user", "content": prompt}]
        )
        
        return response.content[0].text
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of all providers."""
        return {
            "available_providers": self.available_providers,
            "failed_providers": list(self.failed_providers),
            "primary_provider": self.config.primary_provider,
            "fallback_providers": self.config.fallback_providers,
            "total_providers": len(self.available_providers),
            "provider_status": {
                provider: {
                    "available": provider in self.available_providers,
                    "failed": provider in self.failed_providers,
                    "model": getattr(self.config, f"{provider}_model", "unknown")
                }
                for provider in ["gemini", "groq", "openrouter", "openai", "anthropic"]
            }
        }
    
    def test_all_providers(self) -> Dict[str, bool]:
        """Test all providers and return their status."""
        results = {}
        
        for provider in ["gemini", "groq", "openrouter", "openai", "anthropic"]:
            try:
                test_prompt = "Hello, respond with just 'OK'"
                result = self._generate_with_provider(provider, test_prompt, max_tokens=10)
                results[provider] = bool(result and len(result.strip()) > 0)
            except Exception as e:
                logger.debug(f"{provider} test failed: {e}")
                results[provider] = False
        
        return results

# Backward compatibility alias
LLMManager = CloudLLMManager
