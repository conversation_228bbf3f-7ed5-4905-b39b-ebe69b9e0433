"""
File Manager for handling file system operations in the WebDev Agent.
Provides safe file creation, modification, and project structure management.
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import hashlib

class FileManager:
    """Manages file system operations with safety checks and backup functionality."""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path).resolve()
        self.backup_dir = self.base_path / ".webdev_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # Track file operations
        self.operations_log = []
        self.created_files = []
        self.modified_files = []
    
    def create_file(self, file_path: str, content: str, backup: bool = True) -> bool:
        """Create a new file with the given content."""
        full_path = self.base_path / file_path
        
        try:
            # Create parent directories if they don't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Backup existing file if it exists
            if full_path.exists() and backup:
                self._backup_file(full_path)
            
            # Write the file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Log the operation
            self._log_operation("create", file_path, full_path.stat().st_size)
            self.created_files.append(str(file_path))
            
            return True
            
        except Exception as e:
            print(f"Error creating file {file_path}: {e}")
            return False
    
    def modify_file(self, file_path: str, content: str, backup: bool = True) -> bool:
        """Modify an existing file with new content."""
        full_path = self.base_path / file_path
        
        if not full_path.exists():
            print(f"File {file_path} does not exist. Use create_file instead.")
            return False
        
        try:
            # Backup existing file
            if backup:
                self._backup_file(full_path)
            
            # Write the new content
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Log the operation
            self._log_operation("modify", file_path, full_path.stat().st_size)
            self.modified_files.append(str(file_path))
            
            return True
            
        except Exception as e:
            print(f"Error modifying file {file_path}: {e}")
            return False
    
    def append_to_file(self, file_path: str, content: str, backup: bool = True) -> bool:
        """Append content to an existing file."""
        full_path = self.base_path / file_path
        
        try:
            # Backup existing file if it exists
            if full_path.exists() and backup:
                self._backup_file(full_path)
            
            # Create parent directories if they don't exist
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Append the content
            with open(full_path, 'a', encoding='utf-8') as f:
                f.write(content)
            
            # Log the operation
            self._log_operation("append", file_path, len(content))
            if str(file_path) not in self.modified_files:
                self.modified_files.append(str(file_path))
            
            return True
            
        except Exception as e:
            print(f"Error appending to file {file_path}: {e}")
            return False
    
    def read_file(self, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        full_path = self.base_path / file_path
        
        if not full_path.exists():
            return None
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None
    
    def file_exists(self, file_path: str) -> bool:
        """Check if a file exists."""
        full_path = self.base_path / file_path
        return full_path.exists() and full_path.is_file()
    
    def directory_exists(self, dir_path: str) -> bool:
        """Check if a directory exists."""
        full_path = self.base_path / dir_path
        return full_path.exists() and full_path.is_dir()
    
    def create_directory(self, dir_path: str) -> bool:
        """Create a directory and its parents if they don't exist."""
        full_path = self.base_path / dir_path
        
        try:
            full_path.mkdir(parents=True, exist_ok=True)
            self._log_operation("create_dir", dir_path, 0)
            return True
        except Exception as e:
            print(f"Error creating directory {dir_path}: {e}")
            return False
    
    def list_files(self, dir_path: str = ".", pattern: str = "*") -> List[str]:
        """List files in a directory matching a pattern."""
        full_path = self.base_path / dir_path
        
        if not full_path.exists():
            return []
        
        try:
            files = []
            for file_path in full_path.glob(pattern):
                if file_path.is_file():
                    relative_path = file_path.relative_to(self.base_path)
                    files.append(str(relative_path))
            return sorted(files)
        except Exception as e:
            print(f"Error listing files in {dir_path}: {e}")
            return []
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get information about a file."""
        full_path = self.base_path / file_path
        
        if not full_path.exists():
            return None
        
        try:
            stat = full_path.stat()
            return {
                "path": str(file_path),
                "size": stat.st_size,
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "is_file": full_path.is_file(),
                "is_directory": full_path.is_dir(),
                "extension": full_path.suffix,
                "name": full_path.name
            }
        except Exception as e:
            print(f"Error getting file info for {file_path}: {e}")
            return None
    
    def copy_file(self, src_path: str, dst_path: str) -> bool:
        """Copy a file from source to destination."""
        src_full = self.base_path / src_path
        dst_full = self.base_path / dst_path
        
        if not src_full.exists():
            print(f"Source file {src_path} does not exist")
            return False
        
        try:
            # Create destination directory if needed
            dst_full.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy the file
            shutil.copy2(src_full, dst_full)
            
            self._log_operation("copy", f"{src_path} -> {dst_path}", dst_full.stat().st_size)
            self.created_files.append(str(dst_path))
            
            return True
        except Exception as e:
            print(f"Error copying file {src_path} to {dst_path}: {e}")
            return False
    
    def delete_file(self, file_path: str, backup: bool = True) -> bool:
        """Delete a file with optional backup."""
        full_path = self.base_path / file_path
        
        if not full_path.exists():
            print(f"File {file_path} does not exist")
            return False
        
        try:
            # Backup before deletion
            if backup:
                self._backup_file(full_path)
            
            # Delete the file
            full_path.unlink()
            
            self._log_operation("delete", file_path, 0)
            
            return True
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")
            return False
    
    def _backup_file(self, file_path: Path) -> None:
        """Create a backup of a file."""
        if not file_path.exists():
            return
        
        # Generate backup filename with timestamp and hash
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_hash = self._get_file_hash(file_path)[:8]
        backup_name = f"{file_path.name}_{timestamp}_{file_hash}"
        
        backup_path = self.backup_dir / backup_name
        
        try:
            shutil.copy2(file_path, backup_path)
        except Exception as e:
            print(f"Warning: Could not backup file {file_path}: {e}")
    
    def _get_file_hash(self, file_path: Path) -> str:
        """Get MD5 hash of a file."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return "unknown"
    
    def _log_operation(self, operation: str, path: str, size: int) -> None:
        """Log a file operation."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "path": path,
            "size": size
        }
        self.operations_log.append(log_entry)
    
    def get_operations_log(self) -> List[Dict[str, Any]]:
        """Get the operations log."""
        return self.operations_log.copy()
    
    def get_created_files(self) -> List[str]:
        """Get list of files created in this session."""
        return self.created_files.copy()
    
    def get_modified_files(self) -> List[str]:
        """Get list of files modified in this session."""
        return self.modified_files.copy()
    
    def save_operations_log(self, log_file: str = "file_operations.json") -> bool:
        """Save the operations log to a file."""
        try:
            log_path = self.base_path / log_file
            with open(log_path, 'w') as f:
                json.dump(self.operations_log, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving operations log: {e}")
            return False
    
    def cleanup_backups(self, days_old: int = 7) -> int:
        """Clean up old backup files."""
        if not self.backup_dir.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            for backup_file in self.backup_dir.iterdir():
                if backup_file.is_file() and backup_file.stat().st_mtime < cutoff_time:
                    backup_file.unlink()
                    deleted_count += 1
        except Exception as e:
            print(f"Error cleaning up backups: {e}")
        
        return deleted_count
