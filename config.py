"""
Configuration management for the WebDev Agent.
Handles local LLM settings, cloud API fallbacks, and project settings.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict, field, field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class LLMConfig:
    """Configuration for cloud LLM providers with automatic fallback."""
    # Primary provider selection
    primary_provider: str = "gemini"
    fallback_providers: list = field(default_factory=lambda: ["groq", "openrouter", "openai"])

    # Gemini (Google) configuration
    gemini_api_key: Optional[str] = None
    gemini_model: str = "gemini-1.5-flash"
    gemini_models: list = field(default_factory=lambda: [
        "gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"
    ])

    # Groq configuration
    groq_api_key: Optional[str] = None
    groq_model: str = "llama-3.1-70b-versatile"
    groq_models: list = field(default_factory=lambda: [
        "llama-3.1-70b-versatile", "llama-3.1-8b-instant", "mixtral-8x7b-32768"
    ])

    # OpenRouter configuration
    openrouter_api_key: Optional[str] = None
    openrouter_model: str = "meta-llama/llama-3.1-8b-instruct:free"
    openrouter_models: list = field(default_factory=lambda: [
        "meta-llama/llama-3.1-8b-instruct:free",
        "microsoft/phi-3-mini-128k-instruct:free",
        "google/gemma-2-9b-it:free"
    ])

    # OpenAI configuration (backup)
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    openai_models: list = field(default_factory=lambda: [
        "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"
    ])

    # Anthropic configuration (backup)
    anthropic_api_key: Optional[str] = None
    anthropic_model: str = "claude-3-haiku-20240307"
    anthropic_models: list = field(default_factory=lambda: [
        "claude-3-haiku-20240307", "claude-3-sonnet-20240229"
    ])

    # Generation settings
    temperature: float = 0.1
    max_tokens: int = 4096
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0

@dataclass
class ProjectConfig:
    """Configuration for project generation."""
    # Framework preferences
    backend_framework: str = "flask"  # flask, fastapi
    frontend_approach: str = "jinja2"  # jinja2, htmx, alpine
    database: str = "sqlite"  # sqlite, postgresql, mongodb
    
    # Project structure
    use_blueprints: bool = True
    include_auth: bool = False
    include_api: bool = True
    include_tests: bool = True
    
    # File organization
    separate_models: bool = True
    separate_routes: bool = True
    use_config_file: bool = True

@dataclass
class AgentConfig:
    """Configuration for agent behavior."""
    # Agent settings
    max_iterations: int = 10
    task_timeout: int = 300  # seconds
    memory_retention: int = 100  # number of tasks to remember
    
    # Collaboration settings
    enable_agent_communication: bool = True
    shared_memory: bool = True
    parallel_execution: bool = False
    
    # Error handling
    max_retries: int = 3
    auto_fix_errors: bool = True
    validate_code: bool = True

class Config:
    """Main configuration class for the WebDev Agent."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "webdev_agent_config.json"
        self.config_path = Path(self.config_file)
        
        # Initialize with defaults
        self.llm = LLMConfig()
        self.project = ProjectConfig()
        self.agent = AgentConfig()
        
        # Load from file if exists
        self.load_config()
        
        # Override with environment variables
        self._load_from_env()
    
    def load_config(self) -> None:
        """Load configuration from JSON file."""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                
                if 'llm' in data:
                    self.llm = LLMConfig(**data['llm'])
                if 'project' in data:
                    self.project = ProjectConfig(**data['project'])
                if 'agent' in data:
                    self.agent = AgentConfig(**data['agent'])
                    
            except Exception as e:
                print(f"Warning: Could not load config file: {e}")
    
    def save_config(self) -> None:
        """Save current configuration to JSON file."""
        config_data = {
            'llm': asdict(self.llm),
            'project': asdict(self.project),
            'agent': asdict(self.agent)
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def _load_from_env(self) -> None:
        """Load configuration from environment variables."""
        # Primary provider
        if os.getenv('WEBDEV_PRIMARY_PROVIDER'):
            self.llm.primary_provider = os.getenv('WEBDEV_PRIMARY_PROVIDER')

        # API keys for all providers
        self.llm.gemini_api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        self.llm.groq_api_key = os.getenv('GROQ_API_KEY')
        self.llm.openrouter_api_key = os.getenv('OPENROUTER_API_KEY')
        self.llm.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.llm.anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')

        # Model selection
        if os.getenv('WEBDEV_GEMINI_MODEL'):
            self.llm.gemini_model = os.getenv('WEBDEV_GEMINI_MODEL')
        if os.getenv('WEBDEV_GROQ_MODEL'):
            self.llm.groq_model = os.getenv('WEBDEV_GROQ_MODEL')
        if os.getenv('WEBDEV_OPENROUTER_MODEL'):
            self.llm.openrouter_model = os.getenv('WEBDEV_OPENROUTER_MODEL')

        # Project configuration
        if os.getenv('WEBDEV_BACKEND_FRAMEWORK'):
            self.project.backend_framework = os.getenv('WEBDEV_BACKEND_FRAMEWORK')

        if os.getenv('WEBDEV_DATABASE'):
            self.project.database = os.getenv('WEBDEV_DATABASE')
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration as dictionary."""
        return asdict(self.llm)
    
    def get_project_config(self) -> Dict[str, Any]:
        """Get project configuration as dictionary."""
        return asdict(self.project)
    
    def get_agent_config(self) -> Dict[str, Any]:
        """Get agent configuration as dictionary."""
        return asdict(self.agent)
    
    def update_config(self, section: str, **kwargs) -> None:
        """Update configuration section with new values."""
        if section == 'llm':
            for key, value in kwargs.items():
                if hasattr(self.llm, key):
                    setattr(self.llm, key, value)
        elif section == 'project':
            for key, value in kwargs.items():
                if hasattr(self.project, key):
                    setattr(self.project, key, value)
        elif section == 'agent':
            for key, value in kwargs.items():
                if hasattr(self.agent, key):
                    setattr(self.agent, key, value)
        
        # Save updated configuration
        self.save_config()

# Global configuration instance
config = Config()

def get_config() -> Config:
    """Get the global configuration instance."""
    return config

def create_default_config() -> None:
    """Create a default configuration file."""
    default_config = Config()
    default_config.save_config()
    print(f"Default configuration created at: {default_config.config_path}")

if __name__ == "__main__":
    create_default_config()
