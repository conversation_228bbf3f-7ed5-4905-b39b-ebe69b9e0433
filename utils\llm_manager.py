"""
LLM Manager for handling both local and cloud-based language models.
Supports Ollama, llama-cpp-python, and cloud APIs with automatic fallback.
"""

import json
import time
import requests
from typing import Dict, Any, Optional, List
from pathlib import Path

from config import get_config, LLMConfig

class LLMManager:
    """Manages local and cloud LLM providers with automatic fallback."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        self.config = config or get_config().llm
        self.local_available = False
        self.cloud_available = False
        
        # Initialize providers
        self._init_local_provider()
        self._init_cloud_provider()
    
    def _init_local_provider(self) -> None:
        """Initialize local LLM provider."""
        if not self.config.use_local:
            return
        
        try:
            if self.config.local_provider == "ollama":
                self._init_ollama()
            elif self.config.local_provider == "llama-cpp":
                self._init_llama_cpp()
            elif self.config.local_provider == "lm-studio":
                self._init_lm_studio()
        except Exception as e:
            print(f"Warning: Could not initialize local LLM provider: {e}")
    
    def _init_ollama(self) -> None:
        """Initialize Ollama provider."""
        try:
            import ollama
            
            # Test connection
            response = requests.get(f"{self.config.local_host}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.config.local_model in model_names:
                    self.local_available = True
                    print(f"Ollama connected successfully with model: {self.config.local_model}")
                else:
                    print(f"Model {self.config.local_model} not found in Ollama. Available models: {model_names}")
            
        except Exception as e:
            print(f"Ollama initialization failed: {e}")
    
    def _init_llama_cpp(self) -> None:
        """Initialize llama-cpp-python provider."""
        try:
            from llama_cpp import Llama
            
            # This would require a model file path
            # For now, just check if the library is available
            self.local_available = True
            print("llama-cpp-python is available")
            
        except ImportError:
            print("llama-cpp-python not installed")
    
    def _init_lm_studio(self) -> None:
        """Initialize LM Studio provider."""
        try:
            # LM Studio typically runs on localhost:1234
            lm_studio_host = self.config.local_host.replace("11434", "1234")
            response = requests.get(f"{lm_studio_host}/v1/models", timeout=5)
            
            if response.status_code == 200:
                self.local_available = True
                print("LM Studio connected successfully")
            
        except Exception as e:
            print(f"LM Studio initialization failed: {e}")
    
    def _init_cloud_provider(self) -> None:
        """Initialize cloud LLM provider."""
        if not self.config.api_key:
            print("No API key provided for cloud LLM")
            return
        
        try:
            if self.config.cloud_provider == "openai":
                self._test_openai()
            elif self.config.cloud_provider == "anthropic":
                self._test_anthropic()
            elif self.config.cloud_provider == "google":
                self._test_google()
        except Exception as e:
            print(f"Cloud LLM initialization failed: {e}")
    
    def _test_openai(self) -> None:
        """Test OpenAI API connection."""
        try:
            import openai
            
            client = openai.OpenAI(api_key=self.config.api_key)
            # Test with a minimal request
            response = client.chat.completions.create(
                model=self.config.cloud_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )
            
            if response:
                self.cloud_available = True
                print("OpenAI API connected successfully")
                
        except Exception as e:
            print(f"OpenAI API test failed: {e}")
    
    def _test_anthropic(self) -> None:
        """Test Anthropic API connection."""
        try:
            import anthropic
            
            client = anthropic.Anthropic(api_key=self.config.api_key)
            # Test connection (this is a placeholder - adjust based on actual API)
            self.cloud_available = True
            print("Anthropic API configured")
            
        except Exception as e:
            print(f"Anthropic API test failed: {e}")
    
    def _test_google(self) -> None:
        """Test Google API connection."""
        try:
            # Placeholder for Google API testing
            self.cloud_available = True
            print("Google API configured")
            
        except Exception as e:
            print(f"Google API test failed: {e}")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using available LLM provider."""
        # Try local first if available and preferred
        if self.config.use_local and self.local_available:
            try:
                return self._generate_local(prompt, **kwargs)
            except Exception as e:
                print(f"Local LLM generation failed: {e}")
                if self.cloud_available:
                    print("Falling back to cloud LLM...")
                    return self._generate_cloud(prompt, **kwargs)
                else:
                    raise Exception("No available LLM providers")
        
        # Use cloud provider
        elif self.cloud_available:
            return self._generate_cloud(prompt, **kwargs)
        
        else:
            raise Exception("No available LLM providers")
    
    def _generate_local(self, prompt: str, **kwargs) -> str:
        """Generate text using local LLM."""
        if self.config.local_provider == "ollama":
            return self._generate_ollama(prompt, **kwargs)
        elif self.config.local_provider == "llama-cpp":
            return self._generate_llama_cpp(prompt, **kwargs)
        elif self.config.local_provider == "lm-studio":
            return self._generate_lm_studio(prompt, **kwargs)
        else:
            raise Exception(f"Unsupported local provider: {self.config.local_provider}")
    
    def _generate_ollama(self, prompt: str, **kwargs) -> str:
        """Generate text using Ollama."""
        try:
            import ollama
            
            response = ollama.chat(
                model=self.config.local_model,
                messages=[{"role": "user", "content": prompt}],
                options={
                    "temperature": kwargs.get("temperature", self.config.temperature),
                    "num_predict": kwargs.get("max_tokens", self.config.max_tokens),
                }
            )
            
            return response['message']['content']
            
        except Exception as e:
            # Fallback to direct API call
            return self._generate_ollama_api(prompt, **kwargs)
    
    def _generate_ollama_api(self, prompt: str, **kwargs) -> str:
        """Generate text using Ollama REST API."""
        url = f"{self.config.local_host}/api/generate"
        
        payload = {
            "model": self.config.local_model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get("temperature", self.config.temperature),
                "num_predict": kwargs.get("max_tokens", self.config.max_tokens),
            }
        }
        
        response = requests.post(url, json=payload, timeout=self.config.timeout)
        response.raise_for_status()
        
        return response.json()["response"]
    
    def _generate_llama_cpp(self, prompt: str, **kwargs) -> str:
        """Generate text using llama-cpp-python."""
        try:
            from llama_cpp import Llama
            
            # This would need proper model initialization
            # Placeholder implementation
            return "llama-cpp-python response placeholder"
            
        except Exception as e:
            raise Exception(f"llama-cpp generation failed: {e}")
    
    def _generate_lm_studio(self, prompt: str, **kwargs) -> str:
        """Generate text using LM Studio API."""
        lm_studio_host = self.config.local_host.replace("11434", "1234")
        url = f"{lm_studio_host}/v1/chat/completions"
        
        payload = {
            "model": self.config.local_model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
        }
        
        response = requests.post(url, json=payload, timeout=self.config.timeout)
        response.raise_for_status()
        
        return response.json()["choices"][0]["message"]["content"]
    
    def _generate_cloud(self, prompt: str, **kwargs) -> str:
        """Generate text using cloud LLM."""
        if self.config.cloud_provider == "openai":
            return self._generate_openai(prompt, **kwargs)
        elif self.config.cloud_provider == "anthropic":
            return self._generate_anthropic(prompt, **kwargs)
        elif self.config.cloud_provider == "google":
            return self._generate_google(prompt, **kwargs)
        else:
            raise Exception(f"Unsupported cloud provider: {self.config.cloud_provider}")
    
    def _generate_openai(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenAI API."""
        try:
            import openai
            
            client = openai.OpenAI(api_key=self.config.api_key)
            
            response = client.chat.completions.create(
                model=self.config.cloud_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get("temperature", self.config.temperature),
                max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"OpenAI generation failed: {e}")
    
    def _generate_anthropic(self, prompt: str, **kwargs) -> str:
        """Generate text using Anthropic API."""
        try:
            import anthropic

            client = anthropic.Anthropic(api_key=self.config.api_key)

            response = client.messages.create(
                model=self.config.cloud_model,
                max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
                temperature=kwargs.get("temperature", self.config.temperature),
                messages=[{"role": "user", "content": prompt}]
            )

            return response.content[0].text

        except Exception as e:
            raise Exception(f"Anthropic generation failed: {e}")

    def _generate_google(self, prompt: str, **kwargs) -> str:
        """Generate text using Google API."""
        try:
            import google.generativeai as genai

            genai.configure(api_key=self.config.api_key)
            model = genai.GenerativeModel(self.config.cloud_model)

            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=kwargs.get("temperature", self.config.temperature),
                    max_output_tokens=kwargs.get("max_tokens", self.config.max_tokens),
                )
            )

            return response.text

        except Exception as e:
            raise Exception(f"Google generation failed: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get LLM manager status."""
        return {
            "local_available": self.local_available,
            "cloud_available": self.cloud_available,
            "local_provider": self.config.local_provider,
            "cloud_provider": self.config.cloud_provider,
            "local_model": self.config.local_model,
            "cloud_model": self.config.cloud_model,
            "use_local": self.config.use_local
        }
    
    def test_connection(self) -> Dict[str, bool]:
        """Test all available connections."""
        results = {
            "local": False,
            "cloud": False
        }
        
        # Test local
        if self.config.use_local:
            try:
                test_response = self._generate_local("Hello", max_tokens=1)
                results["local"] = bool(test_response)
            except:
                results["local"] = False
        
        # Test cloud
        if self.config.api_key:
            try:
                test_response = self._generate_cloud("Hello", max_tokens=1)
                results["cloud"] = bool(test_response)
            except:
                results["cloud"] = False
        
        return results
