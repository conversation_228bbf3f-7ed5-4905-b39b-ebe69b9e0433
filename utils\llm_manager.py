"""
LLM Manager for handling both local and cloud-based language models.
Supports Ollama, llama-cpp-python, and cloud APIs with automatic fallback.
"""

import json
import time
import requests
from typing import Dict, Any, Optional, List

from config import get_config, LLMConfig

class LLMManager:
    """Manages local and cloud LLM providers with automatic fallback."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        self.config = config or get_config().llm
        self.cloud_available = False
        
        # Initialize providers
        self._init_cloud_provider()
    
    
        
        
    
    
    
    
    
    
    
    def _init_cloud_provider(self) -> None:
        """Initialize cloud LLM providers."""
        providers = {
            "gemini": self._test_gemini,
            "groq": self._test_groq,
            "openrouter": self._test_openrouter,
            "google": self._test_google
        }
        
        for provider, test_func in providers.items():
            try:
                test_func()
            except Exception as e:
                print(f"Cloud LLM provider {provider} initialization failed: {e}")
    
    
    
    def _test_google(self) -> None:
        """Test Google API connection."""
        try:
            # Placeholder for Google API testing
            self.cloud_available = True
            print("Google API configured")
            
        except Exception as e:
            print(f"Google API test failed: {e}")

    def _test_gemini(self) -> None:
        """Test Gemini API connection."""
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.config.api_key)
            model = genai.GenerativeModel(self.config.cloud_model)
            response = model.generate_content("Hello", generation_config=genai.types.GenerationConfig(max_output_tokens=1))
            if response:
                self.cloud_available = True
                print("Gemini API connected successfully")
        except Exception as e:
            print(f"Gemini API test failed: {e}")

    def _test_groq(self) -> None:
        """Test Groq API connection."""
        try:
            from groq import Groq
            client = Groq(api_key=self.config.api_key)
            response = client.chat.completions.create(
                model=self.config.cloud_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )
            if response:
                self.cloud_available = True
                print("Groq API connected successfully")
        except Exception as e:
            print(f"Groq API test failed: {e}")

    def _test_openrouter(self) -> None:
        """Test OpenRouter API connection."""
        try:
            import openai

            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=self.config.api_key,
            )
            # Test with a minimal request
            response = client.chat.completions.create(
                model=self.config.cloud_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )

            if response:
                self.cloud_available = True
                print("OpenRouter API connected successfully")

        except Exception as e:
            print(f"OpenRouter API test failed: {e}")
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using available cloud LLM providers with fallback."""
        providers = ["gemini", "groq", "openrouter", "google"]
        
        for provider in providers:
            try:
                self.config.cloud_provider = provider
                return self._generate_cloud(prompt, **kwargs)
            except Exception as e:
                print(f"Cloud LLM provider {provider} failed: {e}")
                continue
        
        raise Exception("All cloud LLM providers failed")
    
    
    
    
    
    
    
    
    
    def _generate_cloud(self, prompt: str, **kwargs) -> str:
        """Generate text using cloud LLM."""
        if self.config.cloud_provider == "gemini":
            return self._generate_gemini(prompt, **kwargs)
        elif self.config.cloud_provider == "groq":
            return self._generate_groq(prompt, **kwargs)
        elif self.config.cloud_provider == "google":
            return self._generate_google(prompt, **kwargs)
        elif self.config.cloud_provider == "openrouter":
            return self._generate_openrouter(prompt, **kwargs)
        else:
            raise Exception(f"Unsupported cloud provider: {self.config.cloud_provider}")
    
    

    def _generate_google(self, prompt: str, **kwargs) -> str:
        """Generate text using Google API."""
        try:
            import google.generativeai as genai

            genai.configure(api_key=self.config.api_key)
            model = genai.GenerativeModel(self.config.cloud_model)

            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=kwargs.get("temperature", self.config.temperature),
                    max_output_tokens=kwargs.get("max_tokens", self.config.max_tokens),
                )
            )

            return response.text

        except Exception as e:
            raise Exception(f"Google generation failed: {e}")

    def _generate_gemini(self, prompt: str, **kwargs) -> str:
        """Generate text using Gemini API."""
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.config.api_key)
            model = genai.GenerativeModel(self.config.cloud_model)
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=kwargs.get("temperature", self.config.temperature),
                    max_output_tokens=kwargs.get("max_tokens", self.config.max_tokens),
                )
            )
            return response.text
        except Exception as e:
            raise Exception(f"Gemini generation failed: {e}")

    def _generate_groq(self, prompt: str, **kwargs) -> str:
        """Generate text using Groq API."""
        try:
            from groq import Groq
            client = Groq(api_key=self.config.api_key)
            response = client.chat.completions.create(
                model=self.config.cloud_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get("temperature", self.config.temperature),
                max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"Groq generation failed: {e}")

    def _generate_openrouter(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenRouter API."""
        try:
            import openai

            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=self.config.api_key,
            )

            model = self.config.cloud_model
            if model in self.config.openrouter_models:
                print(f"Using OpenRouter free model: {model}")

            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=kwargs.get("temperature", self.config.temperature),
                max_tokens=kwargs.get("max_tokens", self.config.max_tokens),
            )

            return response.choices[0].message.content

        except Exception as e:
            raise Exception(f"OpenRouter generation failed: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get LLM manager status."""
        return {
            "cloud_available": self.cloud_available,
            "cloud_provider": self.config.cloud_provider,
            "cloud_model": self.config.cloud_model,
        }
    
    def test_connection(self) -> Dict[str, bool]:
        """Test all available connections."""
        results = {
            "cloud": False
        }
        
        # Test cloud
        if self.config.api_key:
            try:
                test_response = self._generate_cloud("Hello", max_tokens=1)
                results["cloud"] = bool(test_response)
            except:
                results["cloud"] = False
        
        return results
