"""
LLM Manager - Wrapper for the new Cloud LLM Manager.
Provides backward compatibility while using the new cloud-based system.
"""

from typing import Dict, Any, Optional
from .cloud_llm_manager import Cloud<PERSON>MManager
from config import get_config, LLMConfig

class LLMManager:
    """Manages cloud LLM providers with automatic fallback."""
    
    def __init__(self, config: Optional[LLMConfig] = None):
        self.config = config or get_config().llm
        self.cloud_manager = CloudLLMManager(self.config)
        
        # Backward compatibility properties
        self.local_available = False  # No local support anymore
        self.cloud_available = len(self.cloud_manager.available_providers) > 0
    
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text using available cloud providers."""
        return self.cloud_manager.generate(prompt, **kwargs)
    
    def get_status(self) -> Dict[str, Any]:
        """Get LLM manager status."""
        cloud_status = self.cloud_manager.get_status()
        
        return {
            "local_available": False,
            "cloud_available": self.cloud_available,
            "local_provider": None,
            "cloud_provider": self.config.primary_provider,
            "local_model": None,
            "cloud_model": getattr(self.config, f"{self.config.primary_provider}_model", "unknown"),
            "use_local": False,
            "available_providers": cloud_status["available_providers"],
            "failed_providers": cloud_status["failed_providers"],
            "provider_details": cloud_status["provider_status"]
        }
    
    def test_connection(self) -> Dict[str, bool]:
        """Test all available connections."""
        return {
            "local": False,
            "cloud": self.cloud_available,
            "providers": self.cloud_manager.test_all_providers()
        }
