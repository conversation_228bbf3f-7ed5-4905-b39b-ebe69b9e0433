#!/usr/bin/env python3
"""
Test script for WebDev Agent Cloud Setup.
Verifies that all cloud LLM providers are working correctly.
"""

import sys
import os
import time
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_environment_setup():
    """Test environment variables and configuration."""
    print("🧪 Testing environment setup...")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment")
    
    # Check for API keys
    api_keys = {
        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY'),
        'GROQ_API_KEY': os.getenv('GROQ_API_KEY'),
        'OPENROUTER_API_KEY': os.getenv('OPENROUTER_API_KEY'),
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY')
    }
    
    configured_keys = {k: v for k, v in api_keys.items() if v}
    
    print(f"   Configured API keys: {len(configured_keys)}")
    for key in configured_keys:
        provider = key.replace('_API_KEY', '').lower()
        print(f"   ✅ {provider.title()}")
    
    if not configured_keys:
        print("❌ No API keys found!")
        print("💡 Run: python setup_cloud.py")
        return False
    
    return True

def test_imports():
    """Test that all required modules can be imported."""
    print("\n🧪 Testing imports...")
    
    required_modules = [
        ('config', 'Configuration system'),
        ('utils.cloud_llm_manager', 'Cloud LLM Manager'),
        ('main', 'Main WebDev Agent'),
        ('web_interface', 'Web Interface')
    ]
    
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {description}")
        except ImportError as e:
            print(f"❌ {description}: {e}")
            return False
    
    return True

def test_cloud_providers():
    """Test individual cloud providers."""
    print("\n🧪 Testing cloud providers...")
    
    try:
        from utils.cloud_llm_manager import CloudLLMManager
        
        llm_manager = CloudLLMManager()
        
        print(f"   Available providers: {llm_manager.available_providers}")
        print(f"   Failed providers: {list(llm_manager.failed_providers)}")
        
        if not llm_manager.available_providers:
            print("❌ No providers available!")
            return False
        
        # Test each provider individually
        test_results = llm_manager.test_all_providers()
        
        for provider, success in test_results.items():
            status = "✅" if success else "❌"
            print(f"   {status} {provider.title()}")
        
        successful_providers = [p for p, s in test_results.items() if s]
        
        if successful_providers:
            print(f"✅ {len(successful_providers)} providers working")
            return True
        else:
            print("❌ No providers working!")
            return False
            
    except Exception as e:
        print(f"❌ Provider test failed: {e}")
        return False

def test_llm_generation():
    """Test LLM text generation with fallback."""
    print("\n🧪 Testing LLM generation...")
    
    try:
        from utils.cloud_llm_manager import CloudLLMManager
        
        llm_manager = CloudLLMManager()
        
        # Test simple generation
        test_prompts = [
            "Hello, respond with just 'OK'",
            "What is 2+2? Respond with just the number.",
            "Name one programming language. Just the name."
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            try:
                print(f"   Test {i}: {prompt[:30]}...")
                start_time = time.time()
                
                response = llm_manager.generate(prompt, max_tokens=20)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response and len(response.strip()) > 0:
                    print(f"   ✅ Response: '{response.strip()}' ({response_time:.2f}s)")
                else:
                    print(f"   ❌ Empty response")
                    return False
                    
            except Exception as e:
                print(f"   ❌ Generation failed: {e}")
                return False
        
        print("✅ LLM generation working")
        return True
        
    except Exception as e:
        print(f"❌ LLM generation test failed: {e}")
        return False

def test_agent_initialization():
    """Test WebDev Agent initialization."""
    print("\n🧪 Testing agent initialization...")
    
    try:
        from main import WebDevAgent
        
        # Create agent
        agent = WebDevAgent("./test_project")
        
        # Test status
        status = agent.get_status()
        
        print(f"   Agents initialized: {len(status['agents_status'])}")
        
        for agent_name, agent_status in status['agents_status'].items():
            print(f"   ✅ {agent_name}")
        
        # Test LLM status
        llm_status = status.get('llm_status', {})
        if llm_status.get('cloud_available'):
            print("   ✅ LLM integration working")
        else:
            print("   ❌ LLM integration failed")
            return False
        
        print("✅ Agent initialization successful")
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False

def test_web_interface():
    """Test web interface components."""
    print("\n🧪 Testing web interface...")
    
    try:
        from web_interface import WebDevAgentInterface
        
        # Test interface creation (don't start server)
        interface = WebDevAgentInterface(host='localhost', port=5557)
        
        print("   ✅ Web interface created")
        
        # Test template creation
        interface._create_web_templates()
        
        templates_dir = Path('web_templates')
        if templates_dir.exists() and (templates_dir / 'dashboard.html').exists():
            print("   ✅ Web templates created")
        else:
            print("   ❌ Web templates missing")
            return False
        
        print("✅ Web interface components working")
        return True
        
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        return False

def test_webdev_tools():
    """Test web development tools."""
    print("\n🧪 Testing web development tools...")
    
    try:
        from agents.webdev_tools_agent import WebDevToolsAgent
        from memory.task_memory import TaskMemory
        from utils.llm_manager import LLMManager
        
        # Create tools agent
        memory = TaskMemory(":memory:")
        llm_manager = LLMManager()
        tools_agent = WebDevToolsAgent(memory, llm_manager, ".")
        
        print(f"   Supported tools: {len(tools_agent.get_supported_task_types())}")
        
        # Test a simple task
        from agents.base_agent import AgentTask
        
        task = AgentTask(
            id="test_task",
            type="analyze_code_quality",
            description="Analyze current project"
        )
        
        # This might fail due to missing tools, but should not crash
        try:
            response = tools_agent.execute_task(task)
            print("   ✅ Tools agent execution completed")
        except Exception as e:
            print(f"   ⚠️  Tools execution failed (expected): {e}")
        
        print("✅ Web development tools available")
        return True
        
    except Exception as e:
        print(f"❌ Web dev tools test failed: {e}")
        return False

def run_comprehensive_test():
    """Run a comprehensive end-to-end test."""
    print("\n🧪 Running comprehensive test...")
    
    try:
        from main import WebDevAgent
        
        # Create agent
        agent = WebDevAgent("./test_project")
        
        # Start a session
        session_id = agent.start_session("Test project for cloud setup verification")
        
        print(f"   ✅ Session started: {session_id}")
        
        # Test project analysis
        agent._analyze_existing_project()
        
        if agent.current_project:
            print(f"   ✅ Project analysis completed")
            print(f"      Framework: {agent.current_project.framework}")
            print(f"      Database: {agent.current_project.database}")
        
        # End session
        agent.end_session(
            achievements=["Cloud setup verification completed"],
            notes="Comprehensive test successful"
        )
        
        print("✅ Comprehensive test successful")
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        return False

def main():
    """Run all tests and provide summary."""
    print("🤖 WebDev Agent - Cloud Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Module Imports", test_imports),
        ("Cloud Providers", test_cloud_providers),
        ("LLM Generation", test_llm_generation),
        ("Agent Initialization", test_agent_initialization),
        ("Web Interface", test_web_interface),
        ("WebDev Tools", test_webdev_tools),
        ("Comprehensive Test", run_comprehensive_test),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cloud setup is working perfectly.")
        print("\n🚀 Ready to use:")
        print("   • python app.py web")
        print("   • python app.py 'Create a web application'")
    elif passed >= total * 0.7:  # 70% pass rate
        print("✅ Most tests passed! System should work with minor issues.")
        print("\n💡 Check failed tests above for details.")
    else:
        print("⚠️  Many tests failed. Please check your setup.")
        print("\n💡 Try running: python setup_cloud.py")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Test runner failed: {e}")
        sys.exit(1)
