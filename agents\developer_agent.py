"""
Developer Agent for generating Python code, templates, and project files.
Handles all code generation tasks for web development projects.
"""

import json
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

from .base_agent import BaseAgent, AgentTask, AgentResponse

from utils.llm_manager import LLMManager
from tools.file_manager_tool import FileManagerTool
from tools.tool_manager import ToolManager

class DeveloperAgent(BaseAgent):
    """Agent responsible for code generation and file creation."""
    
    def __init__(self, name: str, role: str, memory: "TaskMemory", llm_manager: LLMManager):
        BaseAgent.__init__(name, role, memory, llm_manager)
        self.tool_manager = ToolManager()
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the developer agent."""
        return """You are a Senior Python Web Developer specializing in Flask and FastAPI applications.

Your role is to:
1. Generate clean, well-documented Python code
2. Create HTML templates using Jinja2
3. Implement database models and migrations
4. Write API routes and business logic
5. Follow Python best practices and PEP 8 standards
6. Create modular, maintainable code structures

Code Generation Guidelines:
- Use type hints for all function parameters and return values
- Include comprehensive docstrings for all functions and classes
- Implement proper error handling with try/catch blocks
- Follow RESTful API design principles
- Use SQLAlchemy for database operations
- Implement proper validation for user inputs
- Include logging for debugging and monitoring
- Write secure code with proper authentication and authorization

When generating code:
- Provide complete, working code files
- Include necessary imports at the top
- Add comments explaining complex logic
- Use meaningful variable and function names
- Implement proper separation of concerns
- Include basic error handling and validation

Focus on creating production-ready code that is secure, efficient, and maintainable."""
    
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        return [
            "create_file",
            "modify_file",
            "create_project_structure",
            "create_main_app",
            "create_routes",
            "create_models",
            "create_templates",
            "create_config",
            "create_database",
            "implement_feature",
            "refactor_code",
            "add_authentication",
            "create_api_endpoints"
        ]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a development task."""
        if "command" in task.description:
            command = task.description.split("command:", 1)[1].strip()
            return self.tool_manager.execute_tool("terminal", command=command)
        elif task.type == "create_file":
            return self._create_file(task)
        elif task.type == "modify_file":
            return self._modify_file(task)
        elif task.type == "create_project_structure":
            return self._create_project_structure(task)
        elif task.type == "create_main_app":
            return self._create_main_app(task)
        elif task.type == "create_routes":
            return self._create_routes(task)
        elif task.type == "create_models":
            return self._create_models(task)
        elif task.type == "create_templates":
            return self._create_templates(task)
        elif task.type == "create_config":
            return self._create_config(task)
        elif task.type == "implement_feature":
            return self._implement_feature(task)
        else:
            return AgentResponse(
                success=False,
                error=f"Unsupported task type: {task.type}"
            )
    
    def _create_file(self, task: AgentTask) -> AgentResponse:
        """Create a new file with specified content."""
        try:
            # Parse task description for file path and requirements
            description = task.description
            
            # Extract file path from description
            if "file:" in description:
                parts = description.split("file:", 1)
                if len(parts) == 2:
                    file_path = parts[1].split()[0].strip()
                    requirements = parts[0].strip()
                else:
                    return AgentResponse(
                        success=False,
                        error="Invalid file creation task format. Use 'requirements file: path/to/file.py'"
                    )
            else:
                return AgentResponse(
                    success=False,
                    error="File path not specified in task description"
                )
            
            # Generate code based on file type and requirements
            file_extension = Path(file_path).suffix
            
            if file_extension == ".py":
                content = self._generate_python_code(requirements, file_path)
            elif file_extension in [".html", ".jinja2"]:
                content = self._generate_template(requirements, file_path)
            elif file_extension in [".css", ".js"]:
                content = self._generate_static_file(requirements, file_path)
            elif file_extension in [".json", ".yaml", ".yml"]:
                content = self._generate_config_file(requirements, file_path)
            else:
                content = self._generate_generic_file(requirements, file_path)
            
            # Create the file
            result = self.tool_manager.execute_tool("file_manager", operation="write", path=file_path, content=content)
            
            if "error" not in result:
                return AgentResponse(
                    success=True,
                    result={"file_path": file_path, "content_length": len(content)},
                    files_created=[file_path]
                )
            else:
                return AgentResponse(
                    success=False,
                    error=f"Failed to create file: {result['error']}"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating file: {str(e)}"
            )
    
    def _modify_file(self, task: AgentTask) -> AgentResponse:
        """Modify an existing file."""
        try:
            description = task.description
            
            # Extract file path and modification requirements
            if "file:" in description:
                parts = description.split("file:", 1)
                if len(parts) == 2:
                    file_path = parts[1].split()[0].strip()
                    requirements = parts[0].strip()
                else:
                    return AgentResponse(
                        success=False,
                        error="Invalid file modification task format"
                    )
            else:
                return AgentResponse(
                    success=False,
                    error="File path not specified in task description"
                )
            
            # Read existing file
            read_result = self.tool_manager.execute_tool("file_manager", operation="read", path=file_path)
            if "error" in read_result:
                return AgentResponse(
                    success=False,
                    error=f"File does not exist: {file_path}"
                )
            existing_content = read_result["content"]
            
            # Generate modified content
            modified_content = self._modify_code(existing_content, requirements, file_path)
            
            # Update the file
            write_result = self.tool_manager.execute_tool("file_manager", operation="write", path=file_path, content=modified_content)
            
            if "error" not in write_result:
                return AgentResponse(
                    success=True,
                    result={"file_path": file_path, "content_length": len(modified_content)},
                    files_modified=[file_path]
                )
            else:
                return AgentResponse(
                    success=False,
                    error=f"Failed to modify file: {write_result['error']}"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error modifying file: {str(e)}"
            )
    
    def _create_project_structure(self, task: AgentTask) -> AgentResponse:
        """Create the basic project directory structure."""
        try:
            description = task.description
            
            # Determine project type from description
            if "fastapi" in description.lower():
                structure = self._get_fastapi_structure()
            else:
                structure = self._get_flask_structure()
            
            created_dirs = []
            created_files = []
            
            # Create directories
            for directory in structure["directories"]:
                result = self.tool_manager.execute_tool("file_manager", operation="write", path=directory, content="")
                if "error" not in result:
                    created_dirs.append(directory)
            
            # Create initial files
            for file_info in structure["files"]:
                file_path = file_info["path"]
                content = file_info["content"]
                
                result = self.tool_manager.execute_tool("file_manager", operation="write", path=file_path, content=content)
                if "error" not in result:
                    created_files.append(file_path)
            
            return AgentResponse(
                success=True,
                result={
                    "directories_created": created_dirs,
                    "files_created": created_files,
                    "structure_type": structure["type"]
                },
                files_created=created_files
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating project structure: {str(e)}"
            )
    
    def _create_main_app(self, task: AgentTask) -> AgentResponse:
        """Create the main application file."""
        try:
            description = task.description
            
            if "fastapi" in description.lower():
                content = self._generate_fastapi_main()
                file_path = "main.py"
            else:
                content = self._generate_flask_main()
                file_path = "app.py"
            
            result = self.tool_manager.execute_tool("file_manager", operation="write", path=file_path, content=content)
            
            if "error" not in result:
                return AgentResponse(
                    success=True,
                    result={"file_path": file_path},
                    files_created=[file_path]
                )
            else:
                return AgentResponse(
                    success=False,
                    error=f"Failed to create main app file: {result['error']}"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating main app: {str(e)}"
            )
    
    def _generate_python_code(self, requirements: str, file_path: str) -> str:
        """Generate Python code based on requirements."""
        prompt = f"""
        Generate Python code for the file: {file_path}
        
        Requirements: {requirements}
        
        The code should:
        1. Follow PEP 8 standards
        2. Include proper type hints
        3. Have comprehensive docstrings
        4. Include error handling
        5. Be production-ready
        
        Provide only the complete Python code without any markdown formatting.
        """
        
        return self.generate_response(prompt)
    
    def _generate_template(self, requirements: str, file_path: str) -> str:
        """Generate HTML template based on requirements."""
        prompt = f"""
        Generate an HTML template for: {file_path}
        
        Requirements: {requirements}
        
        The template should:
        1. Use Jinja2 templating syntax
        2. Include proper HTML5 structure
        3. Be responsive and accessible
        4. Include necessary CSS classes
        5. Follow semantic HTML practices
        
        Provide only the complete HTML template without any markdown formatting.
        """
        
        return self.generate_response(prompt)
    
    def _generate_static_file(self, requirements: str, file_path: str) -> str:
        """Generate CSS or JavaScript file."""
        file_type = "CSS" if file_path.endswith(".css") else "JavaScript"
        
        prompt = f"""
        Generate {file_type} code for: {file_path}
        
        Requirements: {requirements}
        
        The code should be clean, well-commented, and follow best practices.
        Provide only the complete {file_type} code without any markdown formatting.
        """
        
        return self.generate_response(prompt)
    
    def _generate_config_file(self, requirements: str, file_path: str) -> str:
        """Generate configuration file."""
        file_type = Path(file_path).suffix[1:].upper()
        
        prompt = f"""
        Generate a {file_type} configuration file for: {file_path}
        
        Requirements: {requirements}
        
        Provide a well-structured configuration with appropriate defaults.
        Provide only the complete {file_type} content without any markdown formatting.
        """
        
        return self.generate_response(prompt)
    
    def _generate_generic_file(self, requirements: str, file_path: str) -> str:
        """Generate generic file content."""
        prompt = f"""
        Generate content for the file: {file_path}
        
        Requirements: {requirements}
        
        Provide appropriate content based on the file type and requirements.
        """
        
        return self.generate_response(prompt)
    
    def _modify_code(self, existing_content: str, requirements: str, file_path: str) -> str:
        """Modify existing code based on requirements."""
        prompt = f"""
        Modify the following code in file: {file_path}
        
        Current code:
        {existing_content}
        
        Modification requirements: {requirements}
        
        Provide the complete modified code that:
        1. Incorporates the requested changes
        2. Maintains existing functionality
        3. Follows best practices
        4. Includes proper error handling
        
        Provide only the complete modified code without any markdown formatting.
        """
        
        return self.generate_response(prompt)
    
    def _get_flask_structure(self) -> Dict[str, Any]:
        """Get Flask project structure."""
        return {
            "type": "flask",
            "directories": [
                "templates",
                "static/css",
                "static/js",
                "static/images",
                "tests"
            ],
            "files": [
                {
                    "path": "requirements.txt",
                    "content": "Flask==2.3.3\nFlask-SQLAlchemy==3.0.5\nFlask-WTF==1.1.1\nWTForms==3.0.1\n"
                },
                {
                    "path": ".gitignore",
                    "content": "__pycache__/\n*.pyc\n*.pyo\n*.pyd\n.env\ninstance/\n.vscode/\n.idea/\n"
                },
                {
                    "path": "README.md",
                    "content": "# Flask Web Application\n\nA web application built with Flask.\n\n## Setup\n\n1. Install dependencies: `pip install -r requirements.txt`\n2. Run the application: `python app.py`\n"
                }
            ]
        }
    
    def _get_fastapi_structure(self) -> Dict[str, Any]:
        """Get FastAPI project structure."""
        return {
            "type": "fastapi",
            "directories": [
                "routers",
                "models",
                "schemas",
                "database",
                "static",
                "templates",
                "tests"
            ],
            "files": [
                {
                    "path": "requirements.txt",
                    "content": "fastapi==0.104.1\nuvicorn==0.24.0\nsqlalchemy==2.0.23\npydantic==2.5.0\n"
                },
                {
                    "path": ".gitignore",
                    "content": "__pycache__/\n*.pyc\n*.pyo\n*.pyd\n.env\n.vscode/\n.idea/\n"
                },
                {
                    "path": "README.md",
                    "content": "# FastAPI Web Application\n\nA web application built with FastAPI.\n\n## Setup\n\n1. Install dependencies: `pip install -r requirements.txt`\n2. Run the application: `uvicorn main:app --reload`\n"
                }
            ]
        }
    
    def _generate_flask_main(self) -> str:
        """Generate Flask main application file."""
        return '''"""
Flask Web Application
Main application file with basic setup and configuration.
"""

from flask import Flask, render_template, request, jsonify
from flask_sqlalchemy import SQLAlchemy
import os

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)

# Import routes (after app initialization)
from routes import *

@app.route('/')
def index():
    """Home page route."""
    return render_template('index.html')

@app.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'message': 'Application is running'})

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return render_template('500.html'), 500

if __name__ == '__main__':
    # Create database tables
    with app.app_context():
        db.create_all()
    
    # Run the application
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
    
    def _generate_fastapi_main(self) -> str:
        """Generate FastAPI main application file."""
        return '''"""
FastAPI Web Application
Main application file with basic setup and configuration.
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="Web Application",
    description="A web application built with FastAPI",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Home page route."""
    return templates.TemplateResponse("index.html", {"request": {}})

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "Application is running"}

@app.get("/api/info")
async def get_app_info():
    """Get application information."""
    return {
        "name": "Web Application",
        "version": "1.0.0",
        "framework": "FastAPI"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
'''
    
    
    
    def _implement_feature(self, task: AgentTask) -> AgentResponse:
        """Implement a complete feature with multiple files."""
        try:
            feature_description = task.description
            
            prompt = f"""
            Implement the following feature: {feature_description}
            
            Provide a complete implementation including:
            1. Backend code (routes, models, business logic)
            2. Frontend templates
            3. Any necessary configuration
            
            Format the response as JSON with file paths and content:
            {{
                "files": [
                    {{
                        "path": "path/to/file.py",
                        "content": "file content here"
                    }}
                ]
            }}
            """
            
            response = self.generate_response(prompt)
            
            try:
                implementation = json.loads(response)
                created_files = []
                
                for file_info in implementation.get("files", []):
                    file_path = file_info["path"]
                    content = file_info["content"]
                    
                    result = self.tool_manager.execute_tool("file_manager", operation="write", path=file_path, content=content)
                    if "error" not in result:
                        created_files.append(file_path)
                
                return AgentResponse(
                    success=True,
                    result={"feature_implemented": feature_description},
                    files_created=created_files
                )
                
            except json.JSONDecodeError:
                return AgentResponse(
                    success=False,
                    error="Failed to parse implementation response"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error implementing feature: {str(e)}"
            )
