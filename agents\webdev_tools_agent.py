"""
Web Development Tools Agent
Provides comprehensive web development and software engineering tools.
"""

import subprocess
import sys
import os
import json
import requests
from typing import Dict, Any, List, Optional
from pathlib import Path

from .base_agent import BaseAgent, AgentTask, AgentResponse
from memory.task_memory import TaskMemory
from utils.llm_manager import LLMManager
from utils.file_manager import FileManager

class WebDevToolsAgent(BaseAgent):
    """Agent providing comprehensive web development tools and utilities."""
    
    def __init__(self, memory: TaskMemory, llm_manager: LLMManager, project_path: str = "."):
        super().__init__("WebDevTools", "Web Development Tools and Utilities", memory, llm_manager)
        self.project_path = Path(project_path)
        self.file_manager = FileManager(str(self.project_path))
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the web dev tools agent."""
        return """You are a Senior Web Development and Software Engineering Tools Specialist.

Your role is to:
1. Analyze web applications and provide insights
2. Perform code quality analysis and security scanning
3. Manage dependencies and package installations
4. Run development servers and build processes
5. Execute browser automation and testing
6. Perform SEO and performance analysis
7. Generate documentation and API specs
8. Handle deployment and DevOps tasks

Available Tools:
- Code analysis (complexity, security, quality)
- Package management (npm, pip, composer)
- Browser automation (Selenium, Playwright)
- Performance testing and optimization
- SEO analysis and validation
- API testing and documentation
- Database management and migrations
- CI/CD pipeline setup
- Docker containerization
- Security scanning and vulnerability assessment

Always provide detailed analysis and actionable recommendations."""
    
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        return [
            "analyze_code_quality",
            "security_scan",
            "performance_test",
            "seo_analysis",
            "dependency_audit",
            "browser_test",
            "api_test",
            "generate_docs",
            "setup_ci_cd",
            "containerize_app",
            "database_migrate",
            "install_packages",
            "run_dev_server",
            "build_production",
            "deploy_app"
        ]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a web development tools task."""
        task_handlers = {
            "analyze_code_quality": self._analyze_code_quality,
            "security_scan": self._security_scan,
            "performance_test": self._performance_test,
            "seo_analysis": self._seo_analysis,
            "dependency_audit": self._dependency_audit,
            "browser_test": self._browser_test,
            "api_test": self._api_test,
            "generate_docs": self._generate_docs,
            "setup_ci_cd": self._setup_ci_cd,
            "containerize_app": self._containerize_app,
            "database_migrate": self._database_migrate,
            "install_packages": self._install_packages,
            "run_dev_server": self._run_dev_server,
            "build_production": self._build_production,
            "deploy_app": self._deploy_app
        }
        
        handler = task_handlers.get(task.type)
        if handler:
            return handler(task)
        else:
            return AgentResponse(
                success=False,
                error=f"Unsupported task type: {task.type}"
            )
    
    def _analyze_code_quality(self, task: AgentTask) -> AgentResponse:
        """Analyze code quality using multiple tools."""
        try:
            results = {}
            
            # Run flake8 for style analysis
            try:
                result = subprocess.run([
                    sys.executable, "-m", "flake8", ".", "--statistics", "--count"
                ], capture_output=True, text=True, cwd=self.project_path)
                
                results["flake8"] = {
                    "return_code": result.returncode,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            except Exception as e:
                results["flake8"] = {"error": str(e)}
            
            # Run pylint for detailed analysis
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pylint", ".", "--output-format=json"
                ], capture_output=True, text=True, cwd=self.project_path)
                
                results["pylint"] = {
                    "return_code": result.returncode,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            except Exception as e:
                results["pylint"] = {"error": str(e)}
            
            # Run mypy for type checking
            try:
                result = subprocess.run([
                    sys.executable, "-m", "mypy", ".", "--json-report", "/tmp/mypy-report"
                ], capture_output=True, text=True, cwd=self.project_path)
                
                results["mypy"] = {
                    "return_code": result.returncode,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            except Exception as e:
                results["mypy"] = {"error": str(e)}
            
            # Generate summary
            summary = self._generate_quality_summary(results)
            
            return AgentResponse(
                success=True,
                result={
                    "analysis_results": results,
                    "summary": summary,
                    "recommendations": self._get_quality_recommendations(results)
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Code quality analysis failed: {str(e)}"
            )
    
    def _security_scan(self, task: AgentTask) -> AgentResponse:
        """Perform security scanning using bandit and safety."""
        try:
            results = {}
            
            # Run bandit for security issues
            try:
                result = subprocess.run([
                    sys.executable, "-m", "bandit", "-r", ".", "-f", "json"
                ], capture_output=True, text=True, cwd=self.project_path)
                
                results["bandit"] = {
                    "return_code": result.returncode,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            except Exception as e:
                results["bandit"] = {"error": str(e)}
            
            # Run safety for dependency vulnerabilities
            try:
                result = subprocess.run([
                    sys.executable, "-m", "safety", "check", "--json"
                ], capture_output=True, text=True, cwd=self.project_path)
                
                results["safety"] = {
                    "return_code": result.returncode,
                    "output": result.stdout,
                    "errors": result.stderr
                }
            except Exception as e:
                results["safety"] = {"error": str(e)}
            
            return AgentResponse(
                success=True,
                result={
                    "security_scan": results,
                    "recommendations": self._get_security_recommendations(results)
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Security scan failed: {str(e)}"
            )
    
    def _performance_test(self, task: AgentTask) -> AgentResponse:
        """Run performance tests on the application."""
        try:
            url = task.description or "http://localhost:5000"
            
            # Basic performance test using requests
            results = {}
            
            # Test response time
            import time
            start_time = time.time()
            
            try:
                response = requests.get(url, timeout=10)
                end_time = time.time()
                
                results["response_test"] = {
                    "status_code": response.status_code,
                    "response_time": end_time - start_time,
                    "content_length": len(response.content),
                    "headers": dict(response.headers)
                }
            except Exception as e:
                results["response_test"] = {"error": str(e)}
            
            # Load test simulation
            results["load_test"] = self._simulate_load_test(url)
            
            return AgentResponse(
                success=True,
                result={
                    "performance_results": results,
                    "recommendations": self._get_performance_recommendations(results)
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Performance test failed: {str(e)}"
            )
    
    def _seo_analysis(self, task: AgentTask) -> AgentResponse:
        """Analyze SEO aspects of the web application."""
        try:
            url = task.description or "http://localhost:5000"
            
            results = {}
            
            try:
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    results["seo_analysis"] = {
                        "title": soup.title.string if soup.title else None,
                        "meta_description": self._get_meta_content(soup, "description"),
                        "meta_keywords": self._get_meta_content(soup, "keywords"),
                        "h1_tags": len(soup.find_all('h1')),
                        "h2_tags": len(soup.find_all('h2')),
                        "images_without_alt": len([img for img in soup.find_all('img') if not img.get('alt')]),
                        "internal_links": len([a for a in soup.find_all('a') if a.get('href', '').startswith('/')]),
                        "external_links": len([a for a in soup.find_all('a') if a.get('href', '').startswith('http')]),
                        "page_size": len(response.content),
                        "load_time": response.elapsed.total_seconds()
                    }
                else:
                    results["seo_analysis"] = {"error": f"HTTP {response.status_code}"}
                    
            except Exception as e:
                results["seo_analysis"] = {"error": str(e)}
            
            return AgentResponse(
                success=True,
                result={
                    "seo_results": results,
                    "recommendations": self._get_seo_recommendations(results)
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"SEO analysis failed: {str(e)}"
            )
    
    def _dependency_audit(self, task: AgentTask) -> AgentResponse:
        """Audit project dependencies for security and updates."""
        try:
            results = {}
            
            # Check for requirements.txt
            req_file = self.project_path / "requirements.txt"
            if req_file.exists():
                # Run pip-audit if available
                try:
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "list", "--outdated", "--format=json"
                    ], capture_output=True, text=True, cwd=self.project_path)
                    
                    if result.returncode == 0:
                        outdated = json.loads(result.stdout)
                        results["outdated_packages"] = outdated
                    
                except Exception as e:
                    results["outdated_packages"] = {"error": str(e)}
            
            # Check package.json if it exists
            package_json = self.project_path / "package.json"
            if package_json.exists():
                try:
                    result = subprocess.run([
                        "npm", "audit", "--json"
                    ], capture_output=True, text=True, cwd=self.project_path)
                    
                    results["npm_audit"] = {
                        "return_code": result.returncode,
                        "output": result.stdout,
                        "errors": result.stderr
                    }
                except Exception as e:
                    results["npm_audit"] = {"error": str(e)}
            
            return AgentResponse(
                success=True,
                result={
                    "dependency_audit": results,
                    "recommendations": self._get_dependency_recommendations(results)
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Dependency audit failed: {str(e)}"
            )
    
    def _browser_test(self, task: AgentTask) -> AgentResponse:
        """Run browser automation tests."""
        try:
            url = task.description or "http://localhost:5000"
            
            # Try Selenium first, then Playwright
            results = {}
            
            try:
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                
                chrome_options = Options()
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(url)
                
                results["selenium_test"] = {
                    "title": driver.title,
                    "url": driver.current_url,
                    "page_source_length": len(driver.page_source),
                    "success": True
                }
                
                driver.quit()
                
            except Exception as e:
                results["selenium_test"] = {"error": str(e), "success": False}
            
            return AgentResponse(
                success=True,
                result={
                    "browser_test": results,
                    "recommendations": ["Consider adding more comprehensive browser tests"]
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Browser test failed: {str(e)}"
            )
    
    def _generate_quality_summary(self, results: Dict) -> Dict[str, Any]:
        """Generate a summary of code quality results."""
        summary = {
            "overall_score": 0,
            "issues_found": 0,
            "recommendations_count": 0
        }
        
        # Analyze flake8 results
        if "flake8" in results and "output" in results["flake8"]:
            # Parse flake8 output for issue count
            output = results["flake8"]["output"]
            if "total errors" in output.lower():
                # Extract error count
                pass
        
        return summary
    
    def _get_quality_recommendations(self, results: Dict) -> List[str]:
        """Get code quality recommendations."""
        recommendations = []
        
        if "flake8" in results and results["flake8"].get("return_code", 0) != 0:
            recommendations.append("Fix PEP 8 style violations found by flake8")
        
        if "pylint" in results and results["pylint"].get("return_code", 0) != 0:
            recommendations.append("Address pylint warnings and errors")
        
        if "mypy" in results and results["mypy"].get("return_code", 0) != 0:
            recommendations.append("Add type hints and fix mypy type errors")
        
        return recommendations
    
    def _get_security_recommendations(self, results: Dict) -> List[str]:
        """Get security recommendations."""
        recommendations = []
        
        if "bandit" in results and results["bandit"].get("return_code", 0) != 0:
            recommendations.append("Address security issues found by bandit")
        
        if "safety" in results and results["safety"].get("return_code", 0) != 0:
            recommendations.append("Update vulnerable dependencies found by safety")
        
        return recommendations
    
    def _get_performance_recommendations(self, results: Dict) -> List[str]:
        """Get performance recommendations."""
        recommendations = []
        
        response_test = results.get("response_test", {})
        if response_test.get("response_time", 0) > 2.0:
            recommendations.append("Optimize response time (currently > 2 seconds)")
        
        return recommendations
    
    def _get_seo_recommendations(self, results: Dict) -> List[str]:
        """Get SEO recommendations."""
        recommendations = []
        
        seo = results.get("seo_analysis", {})
        
        if not seo.get("title"):
            recommendations.append("Add a page title")
        
        if not seo.get("meta_description"):
            recommendations.append("Add a meta description")
        
        if seo.get("images_without_alt", 0) > 0:
            recommendations.append("Add alt text to images")
        
        return recommendations
    
    def _get_dependency_recommendations(self, results: Dict) -> List[str]:
        """Get dependency recommendations."""
        recommendations = []
        
        if "outdated_packages" in results and results["outdated_packages"]:
            recommendations.append("Update outdated Python packages")
        
        if "npm_audit" in results and results["npm_audit"].get("return_code", 0) != 0:
            recommendations.append("Fix npm security vulnerabilities")
        
        return recommendations
    
    def _simulate_load_test(self, url: str) -> Dict[str, Any]:
        """Simulate a basic load test."""
        import concurrent.futures
        import time
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(url, timeout=5)
                end = time.time()
                return {
                    "status_code": response.status_code,
                    "response_time": end - start,
                    "success": response.status_code == 200
                }
            except Exception as e:
                return {"error": str(e), "success": False}
        
        # Run 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        successful_requests = [r for r in results if r.get("success")]
        
        return {
            "total_requests": len(results),
            "successful_requests": len(successful_requests),
            "average_response_time": sum(r.get("response_time", 0) for r in successful_requests) / len(successful_requests) if successful_requests else 0,
            "success_rate": len(successful_requests) / len(results) if results else 0
        }
    
    def _get_meta_content(self, soup, name: str) -> Optional[str]:
        """Extract meta tag content."""
        meta = soup.find("meta", attrs={"name": name})
        return meta.get("content") if meta else None
    
    def _install_packages(self, task: AgentTask) -> AgentResponse:
        """Install packages using appropriate package manager."""
        try:
            packages = task.description.strip()
            
            # Determine package manager
            if (self.project_path / "package.json").exists():
                # Node.js project
                result = subprocess.run([
                    "npm", "install", packages
                ], capture_output=True, text=True, cwd=self.project_path)
            else:
                # Python project
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", packages
                ], capture_output=True, text=True, cwd=self.project_path)
            
            return AgentResponse(
                success=result.returncode == 0,
                result={
                    "command_output": result.stdout,
                    "command_errors": result.stderr,
                    "return_code": result.returncode
                },
                error=result.stderr if result.returncode != 0 else None
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Package installation failed: {str(e)}"
            )
    
    def _run_dev_server(self, task: AgentTask) -> AgentResponse:
        """Start the development server."""
        try:
            # Detect project type and start appropriate server
            if (self.project_path / "app.py").exists():
                command = [sys.executable, "app.py"]
            elif (self.project_path / "main.py").exists():
                command = [sys.executable, "-m", "uvicorn", "main:app", "--reload"]
            elif (self.project_path / "manage.py").exists():
                command = [sys.executable, "manage.py", "runserver"]
            elif (self.project_path / "package.json").exists():
                command = ["npm", "start"]
            else:
                return AgentResponse(
                    success=False,
                    error="Could not determine how to start the development server"
                )
            
            return AgentResponse(
                success=True,
                result={
                    "command": " ".join(command),
                    "message": "Development server command prepared",
                    "note": "Execute this command to start the server"
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Failed to prepare dev server command: {str(e)}"
            )
