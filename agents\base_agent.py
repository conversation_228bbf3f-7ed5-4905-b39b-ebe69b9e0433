"""
Base Agent class for the WebDev Agent Framework.
Provides common functionality for all specialized agents.
"""

import json
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import asdict
from pathlib import Path

from agents.types import AgentTask, AgentResponse
from utils.llm_manager import LLMManager
from tools.tool_manager import Too<PERSON><PERSON>ana<PERSON>
from config import get_config


class BaseAgent(ABC):
    """Base class for all WebDev agents."""
    
    def __init__(self, name: str, role: str, memory: "TaskMemory", llm_manager: <PERSON>MManager):
        
        self.name = name
        self.role = role
        self.memory = memory
        self.llm_manager = llm_manager
        self.config = get_config()
        self.tool_manager = ToolManager()
        
        # Agent state
        self.current_task: Optional[AgentTask] = None
        self.task_history: List[AgentTask] = []
        self.is_busy = False
        
        # Performance metrics
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.average_task_time = 0.0
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass
    
    @abstractmethod
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a specific task. Must be implemented by subclasses."""
        pass
    
    def can_handle_task(self, task: AgentTask) -> bool:
        """Check if this agent can handle the given task type."""
        return task.type in self.get_supported_task_types()
    
    @abstractmethod
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        pass
    
    def run_task(self, task: AgentTask) -> AgentResponse:
        """Run a task with proper state management and error handling."""
        if self.is_busy:
            return AgentResponse(
                success=False,
                error=f"Agent {self.name} is currently busy with another task"
            )
        
        if not self.can_handle_task(task):
            return AgentResponse(
                success=False,
                error=f"Agent {self.name} cannot handle task type: {task.type}"
            )
        
        # Update task and agent state
        task.status = "in_progress"
        task.started_at = time.time()
        self.current_task = task
        self.is_busy = True
        
        try:
            # Execute the task
            response = self.execute_task(task)
            
            # Update task status based on response
            if response.success:
                task.status = "completed"
                task.result = response.result
                self.tasks_completed += 1
            else:
                task.status = "failed"
                task.error = response.error
                self.tasks_failed += 1
            
            task.completed_at = time.time()
            
            # Update performance metrics
            task_duration = task.completed_at - task.started_at
            self._update_performance_metrics(task_duration)
            
            # Store task in memory
            self.memory.store_task(task)
            self.task_history.append(task)
            
            return response
            
        except Exception as e:
            # Handle unexpected errors
            task.status = "failed"
            task.error = str(e)
            task.completed_at = time.time()
            self.tasks_failed += 1
            
            self.memory.store_task(task)
            self.task_history.append(task)
            
            return AgentResponse(
                success=False,
                error=f"Unexpected error in {self.name}: {str(e)}"
            )
        
        finally:
            # Clean up agent state
            self.current_task = None
            self.is_busy = False
    
    def _update_performance_metrics(self, task_duration: float) -> None:
        """Update agent performance metrics."""
        total_tasks = self.tasks_completed + self.tasks_failed
        if total_tasks > 0:
            self.average_task_time = (
                (self.average_task_time * (total_tasks - 1) + task_duration) / total_tasks
            )
    
    def get_context(self, max_tasks: int = 5) -> str:
        """Get recent task context for the agent."""
        recent_tasks = self.task_history[-max_tasks:] if self.task_history else []
        
        context = f"Agent: {self.name} ({self.role})\n"
        context += f"Tasks completed: {self.tasks_completed}\n"
        context += f"Tasks failed: {self.tasks_failed}\n"
        context += f"Average task time: {self.average_task_time:.2f}s\n\n"
        
        if recent_tasks:
            context += "Recent tasks:\n"
            for task in recent_tasks:
                context += f"- {task.type}: {task.description} ({task.status})\n"
        
        return context
    
    def generate_response(self, prompt: str, context: Optional[str] = None) -> str:
        """Generate a response using the LLM."""
        system_prompt = self.get_system_prompt()
        
        if context:
            full_prompt = f"{system_prompt}\n\nContext:\n{context}\n\nTask:\n{prompt}"
        else:
            full_prompt = f"{system_prompt}\n\nTask:\n{prompt}"
        
        return self.llm_manager.generate(full_prompt)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            "name": self.name,
            "role": self.role,
            "is_busy": self.is_busy,
            "current_task": asdict(self.current_task) if self.current_task else None,
            "tasks_completed": self.tasks_completed,
            "tasks_failed": self.tasks_failed,
            "average_task_time": self.average_task_time,
            "task_history_count": len(self.task_history)
        }
    
    def reset(self) -> None:
        """Reset agent state."""
        self.current_task = None
        self.is_busy = False
        self.task_history.clear()
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.average_task_time = 0.0