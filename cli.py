"""
Command Line Interface for the WebDev Agent.
Provides an interactive interface for managing web development projects.
"""

import click
import json
import time
from pathlib import Path
from typing import Dict, Any, List
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm

from main import WebDevAgent
from config import get_config, create_default_config
from memory.project_memory import ProjectMemory

console = Console()

@click.group()
@click.version_option(version="1.0.0", prog_name="WebDev Agent")
def cli():
    """WebDev Agent - AI-powered web development assistant."""
    pass

@cli.command()
@click.argument('description')
@click.option('--path', '-p', default='.', help='Project directory path')
@click.option('--framework', '-f', type=click.Choice(['flask', 'fastapi']), help='Web framework to use')
@click.option('--database', '-d', type=click.Choice(['sqlite', 'postgresql', 'mongodb']), help='Database to use')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def create(description: str, path: str, framework: str, database: str, verbose: bool):
    """Create a new web application from description."""
    console.print(f"[bold green]Creating new web application[/bold green]")
    console.print(f"Description: {description}")
    console.print(f"Path: {path}")
    
    if framework:
        console.print(f"Framework: {framework}")
    if database:
        console.print(f"Database: {database}")
    
    # Initialize agent
    agent = WebDevAgent(path)
    
    # Update configuration if specified
    config = get_config()
    if framework:
        config.update_config('project', backend_framework=framework)
    if database:
        config.update_config('project', database=database)
    
    # Build application with progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Building application...", total=None)
        
        result = agent.build_application(description)
        
        progress.update(task, completed=True)
    
    # Display results
    if result["success"]:
        console.print("\n[bold green]✅ Application created successfully![/bold green]")
        
        # Create summary table
        table = Table(title="Project Summary")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")
        
        summary = result.get('summary', {})
        for key, value in summary.items():
            table.add_row(key.replace('_', ' ').title(), str(value))
        
        console.print(table)
        
        # Show created files
        if result.get('files_created'):
            console.print("\n[bold]Files Created:[/bold]")
            for file_path in result['files_created']:
                console.print(f"  📄 {file_path}")
        
        # Show next steps
        console.print("\n[bold]Next Steps:[/bold]")
        console.print("1. Install dependencies: [cyan]pip install -r requirements.txt[/cyan]")
        
        if summary.get('framework') == 'flask':
            console.print("2. Run the application: [cyan]python app.py[/cyan]")
        elif summary.get('framework') == 'fastapi':
            console.print("2. Run the application: [cyan]uvicorn main:app --reload[/cyan]")
        
        console.print("3. Run tests: [cyan]pytest tests/[/cyan]")
        
    else:
        console.print(f"\n[bold red]❌ Failed to create application:[/bold red]")
        console.print(f"Error: {result['error']}")

@cli.command()
@click.argument('requirements')
@click.option('--path', '-p', default='.', help='Project directory path')
def continue_project(requirements: str, path: str):
    """Continue development on an existing project."""
    console.print(f"[bold blue]Continuing project development[/bold blue]")
    console.print(f"Additional requirements: {requirements}")
    console.print(f"Path: {path}")
    
    agent = WebDevAgent(path)
    
    # Start session and continue project
    session_id = agent.start_session()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Implementing new features...", total=None)
        
        result = agent.continue_project(requirements)
        
        progress.update(task, completed=True)
    
    if result["success"]:
        console.print("\n[bold green]✅ Project updated successfully![/bold green]")
        
        execution_results = result.get('execution_results', {})
        console.print(f"Tasks completed: {execution_results.get('completed', 0)}")
        console.print(f"Tasks failed: {execution_results.get('failed', 0)}")
        
    else:
        console.print(f"\n[bold red]❌ Failed to update project:[/bold red]")
        console.print(f"Error: {result['error']}")
    
    agent.end_session()

@cli.command()
@click.option('--path', '-p', default='.', help='Project directory path')
def analyze(path: str):
    """Analyze an existing project structure."""
    console.print(f"[bold blue]Analyzing project[/bold blue]")
    console.print(f"Path: {path}")
    
    agent = WebDevAgent(path)
    
    # Analyze the project
    analysis = agent.project_analyzer.analyze_project()
    
    # Display analysis results
    console.print("\n[bold]Project Analysis Results:[/bold]")
    
    # Basic info table
    info_table = Table(title="Project Information")
    info_table.add_column("Property", style="cyan")
    info_table.add_column("Value", style="green")
    
    info_table.add_row("Framework", analysis.framework)
    info_table.add_row("Database", analysis.database)
    info_table.add_row("Structure Type", analysis.structure_type)
    info_table.add_row("Total Files", str(len(analysis.files)))
    info_table.add_row("Total Lines", str(analysis.total_lines))
    info_table.add_row("Complexity Score", str(analysis.complexity_score))
    
    console.print(info_table)
    
    # Files breakdown
    if analysis.files:
        files_table = Table(title="Files Breakdown")
        files_table.add_column("File", style="cyan")
        files_table.add_column("Type", style="yellow")
        files_table.add_column("Lines", style="green")
        files_table.add_column("Functions", style="blue")
        files_table.add_column("Classes", style="magenta")
        
        for file_info in analysis.files[:10]:  # Show first 10 files
            files_table.add_row(
                file_info.path,
                file_info.type,
                str(file_info.lines),
                str(len(file_info.functions)),
                str(len(file_info.classes))
            )
        
        console.print(files_table)
    
    # Recommendations
    if analysis.recommendations:
        console.print("\n[bold]Recommendations:[/bold]")
        for i, rec in enumerate(analysis.recommendations, 1):
            console.print(f"{i}. {rec}")

@cli.command()
def list_projects():
    """List all projects."""
    console.print("[bold blue]All Projects[/bold blue]")
    
    project_memory = ProjectMemory()
    projects = project_memory.list_projects()
    
    if not projects:
        console.print("No projects found.")
        return
    
    table = Table(title="Projects")
    table.add_column("Name", style="cyan")
    table.add_column("Framework", style="yellow")
    table.add_column("Status", style="green")
    table.add_column("Created", style="blue")
    table.add_column("Last Modified", style="magenta")
    table.add_column("Path", style="dim")
    
    for project in projects:
        created_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(project.created_at))
        modified_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(project.last_modified))
        
        table.add_row(
            project.name,
            project.framework,
            project.status,
            created_time,
            modified_time,
            project.path
        )
    
    console.print(table)

@cli.command()
@click.option('--path', '-p', default='.', help='Project directory path')
def test(path: str):
    """Run tests for the project."""
    console.print(f"[bold blue]Running tests[/bold blue]")
    console.print(f"Path: {path}")
    
    agent = WebDevAgent(path)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Running tests...", total=None)
        
        test_results = agent._run_tests()
        
        progress.update(task, completed=True)
    
    if test_results["success"]:
        console.print("\n[bold green]✅ Tests completed![/bold green]")
        
        results = test_results.get("results", {})
        if "test_results" in results:
            test_data = results["test_results"]
            
            # Create test results table
            table = Table(title="Test Results")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Tests", str(test_data.get("total_tests", 0)))
            table.add_row("Passed", str(test_data.get("passed", 0)))
            table.add_row("Failed", str(test_data.get("failed", 0)))
            table.add_row("Skipped", str(test_data.get("skipped", 0)))
            table.add_row("Duration", f"{test_data.get('duration', 0):.2f}s")
            
            console.print(table)
        
        # Show test output if verbose
        if results.get("test_output"):
            console.print("\n[bold]Test Output:[/bold]")
            console.print(Panel(results["test_output"], title="Test Results"))
    
    else:
        console.print(f"\n[bold red]❌ Tests failed:[/bold red]")
        console.print(f"Error: {test_results['error']}")

@cli.command()
def status():
    """Show system status and configuration."""
    console.print("[bold blue]WebDev Agent Status[/bold blue]")
    
    # Show configuration
    config = get_config()
    
    config_table = Table(title="Configuration")
    config_table.add_column("Setting", style="cyan")
    config_table.add_column("Value", style="green")
    
    # LLM configuration
    config_table.add_row("Use Local LLM", str(config.llm.use_local))
    config_table.add_row("Local Provider", config.llm.local_provider)
    config_table.add_row("Local Model", config.llm.local_model)
    config_table.add_row("Cloud Provider", config.llm.cloud_provider)
    config_table.add_row("Cloud Model", config.llm.cloud_model)
    
    # Project configuration
    config_table.add_row("Backend Framework", config.project.backend_framework)
    config_table.add_row("Database", config.project.database)
    config_table.add_row("Frontend Approach", config.project.frontend_approach)
    
    console.print(config_table)
    
    # Test LLM connection
    try:
        from utils.llm_manager import LLMManager
        llm_manager = LLMManager()
        
        console.print("\n[bold]LLM Status:[/bold]")
        status_info = llm_manager.get_status()
        
        status_table = Table()
        status_table.add_column("Provider", style="cyan")
        status_table.add_column("Available", style="green")
        
        status_table.add_row("Local", "✅" if status_info["local_available"] else "❌")
        status_table.add_row("Cloud", "✅" if status_info["cloud_available"] else "❌")
        
        console.print(status_table)
        
    except Exception as e:
        console.print(f"[red]Error checking LLM status: {e}[/red]")

@cli.command()
def config():
    """Configure the WebDev Agent settings."""
    console.print("[bold blue]WebDev Agent Configuration[/bold blue]")
    
    current_config = get_config()
    
    # LLM Configuration
    console.print("\n[bold]LLM Configuration:[/bold]")
    
    use_local = Confirm.ask("Use local LLM?", default=current_config.llm.use_local)
    
    if use_local:
        local_provider = Prompt.ask(
            "Local LLM provider",
            choices=["llama-cpp", "lm-studio"],
            default=current_config.llm.local_provider
        )
        local_model = Prompt.ask("Local model name", default=current_config.llm.local_model)
        
        current_config.update_config('llm', 
            use_local=use_local,
            local_provider=local_provider,
            local_model=local_model
        )
    else:
        cloud_provider = Prompt.ask(
            "Cloud LLM provider",
            choices=["gemini", "groq", "google", "openrouter"],
            default=current_config.llm.cloud_provider
        )
        api_key = Prompt.ask("API Key", password=True)
        
        current_config.update_config('llm',
            use_local=use_local,
            cloud_provider=cloud_provider,
            api_key=api_key
        )
    
    # Project Configuration
    console.print("\n[bold]Project Configuration:[/bold]")
    
    backend_framework = Prompt.ask(
        "Default backend framework",
        choices=["flask", "fastapi"],
        default=current_config.project.backend_framework
    )
    
    database = Prompt.ask(
        "Default database",
        choices=["sqlite", "postgresql", "mongodb"],
        default=current_config.project.database
    )
    
    current_config.update_config('project',
        backend_framework=backend_framework,
        database=database
    )
    
    console.print("\n[bold green]✅ Configuration updated![/bold green]")

@cli.command()
def init():
    """Initialize WebDev Agent in the current directory."""
    console.print("[bold blue]Initializing WebDev Agent[/bold blue]")
    
    # Create default configuration
    create_default_config()
    
    # Create directories
    Path("projects").mkdir(exist_ok=True)
    Path("templates").mkdir(exist_ok=True)
    
    console.print("[bold green]✅ WebDev Agent initialized![/bold green]")
    console.print("Run 'webdev-agent config' to configure your settings.")

if __name__ == "__main__":
    cli()
