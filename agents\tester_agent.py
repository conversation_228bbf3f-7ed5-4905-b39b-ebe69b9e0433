"""
Tester Agent for creating and running test suites.
Handles unit tests, integration tests, and test execution.
"""

import json
import subprocess
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path

from .base_agent import BaseAgent, AgentTask, AgentResponse
from memory.task_memory import TaskMemory
from utils.llm_manager import LLMManager
from utils.file_manager import FileManager

class TesterAgent(BaseAgent):
    """Agent responsible for testing and quality assurance."""
    
    def __init__(self, memory: TaskMemory, llm_manager: LLMManager, project_path: str = "."):
        super().__init__("Tester", "Testing and Quality Assurance", memory, llm_manager)
        self.file_manager = FileManager(project_path)
        self.project_path = Path(project_path)
        self.test_frameworks = ["pytest", "unittest"]
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the tester agent."""
        return """You are a Senior QA Engineer and Test Automation Specialist.

Your role is to:
1. Create comprehensive test suites for Python web applications
2. Write unit tests, integration tests, and end-to-end tests
3. Implement test fixtures and mock objects
4. Run tests and analyze results
5. Ensure code coverage and quality standards
6. Create test data and scenarios

Testing Guidelines:
- Use pytest as the primary testing framework
- Follow AAA pattern (Arrange, Act, Assert)
- Create meaningful test names that describe what is being tested
- Use fixtures for test setup and teardown
- Mock external dependencies and services
- Test both positive and negative scenarios
- Include edge cases and error conditions
- Aim for high code coverage (>80%)
- Write tests that are fast, reliable, and independent

Test Types to Create:
- Unit tests for individual functions and methods
- Integration tests for API endpoints and database operations
- Functional tests for complete user workflows
- Performance tests for critical operations
- Security tests for authentication and authorization

Always provide complete, runnable test code with proper imports and setup."""
    
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        return [
            "create_tests",
            "run_tests",
            "create_unit_tests",
            "create_integration_tests",
            "create_test_fixtures",
            "analyze_coverage",
            "create_test_data",
            "validate_code",
            "run_specific_test",
            "create_test_suite"
        ]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a testing task."""
        if task.type == "create_tests":
            return self._create_tests(task)
        elif task.type == "run_tests":
            return self._run_tests(task)
        elif task.type == "create_unit_tests":
            return self._create_unit_tests(task)
        elif task.type == "create_integration_tests":
            return self._create_integration_tests(task)
        elif task.type == "create_test_fixtures":
            return self._create_test_fixtures(task)
        elif task.type == "analyze_coverage":
            return self._analyze_coverage(task)
        elif task.type == "validate_code":
            return self._validate_code(task)
        elif task.type == "run_specific_test":
            return self._run_specific_test(task)
        else:
            return AgentResponse(
                success=False,
                error=f"Unsupported task type: {task.type}"
            )
    
    def _create_tests(self, task: AgentTask) -> AgentResponse:
        """Create comprehensive test suite for the project."""
        try:
            description = task.description
            
            # Analyze existing code to determine what needs testing
            python_files = self.file_manager.list_files(".", "*.py")
            test_files_created = []
            
            for py_file in python_files:
                if not py_file.startswith("test_") and py_file != "conftest.py":
                    # Read the file to understand what to test
                    content = self.file_manager.read_file(py_file)
                    if content:
                        test_content = self._generate_test_for_file(py_file, content, description)
                        test_file_path = f"tests/test_{Path(py_file).stem}.py"
                        
                        if self.file_manager.create_file(test_file_path, test_content):
                            test_files_created.append(test_file_path)
            
            # Create conftest.py for shared fixtures
            conftest_content = self._generate_conftest()
            if self.file_manager.create_file("tests/conftest.py", conftest_content):
                test_files_created.append("tests/conftest.py")
            
            # Create test requirements
            test_requirements = self._generate_test_requirements()
            if self.file_manager.create_file("tests/requirements.txt", test_requirements):
                test_files_created.append("tests/requirements.txt")
            
            return AgentResponse(
                success=True,
                result={"test_files_created": test_files_created},
                files_created=test_files_created
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating tests: {str(e)}"
            )
    
    def _run_tests(self, task: AgentTask) -> AgentResponse:
        """Run the test suite and return results."""
        try:
            test_path = task.description or "tests/"
            
            # Check if pytest is available
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"],
                    capture_output=True,
                    text=True,
                    cwd=self.project_path
                )
                
                test_output = result.stdout + result.stderr
                success = result.returncode == 0
                
                # Parse test results
                test_results = self._parse_test_results(test_output)
                
                return AgentResponse(
                    success=success,
                    result={
                        "test_output": test_output,
                        "test_results": test_results,
                        "return_code": result.returncode
                    }
                )
                
            except FileNotFoundError:
                # Fallback to unittest if pytest is not available
                result = subprocess.run(
                    [sys.executable, "-m", "unittest", "discover", "-s", test_path, "-v"],
                    capture_output=True,
                    text=True,
                    cwd=self.project_path
                )
                
                test_output = result.stdout + result.stderr
                success = result.returncode == 0
                
                return AgentResponse(
                    success=success,
                    result={
                        "test_output": test_output,
                        "framework": "unittest",
                        "return_code": result.returncode
                    }
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error running tests: {str(e)}"
            )
    
    def _create_unit_tests(self, task: AgentTask) -> AgentResponse:
        """Create unit tests for specific functions or classes."""
        try:
            description = task.description
            
            # Extract file path and function/class to test
            if "file:" in description:
                parts = description.split("file:", 1)
                file_path = parts[1].split()[0].strip()
                test_requirements = parts[0].strip()
            else:
                return AgentResponse(
                    success=False,
                    error="File path not specified for unit test creation"
                )
            
            # Read the source file
            source_content = self.file_manager.read_file(file_path)
            if not source_content:
                return AgentResponse(
                    success=False,
                    error=f"Source file not found: {file_path}"
                )
            
            # Generate unit tests
            test_content = self._generate_unit_tests(source_content, test_requirements, file_path)
            test_file_path = f"tests/test_{Path(file_path).stem}.py"
            
            success = self.file_manager.create_file(test_file_path, test_content)
            
            if success:
                return AgentResponse(
                    success=True,
                    result={"test_file": test_file_path},
                    files_created=[test_file_path]
                )
            else:
                return AgentResponse(
                    success=False,
                    error=f"Failed to create unit test file: {test_file_path}"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating unit tests: {str(e)}"
            )
    
    def _create_integration_tests(self, task: AgentTask) -> AgentResponse:
        """Create integration tests for API endpoints and workflows."""
        try:
            description = task.description
            
            test_content = self._generate_integration_tests(description)
            test_file_path = "tests/test_integration.py"
            
            success = self.file_manager.create_file(test_file_path, test_content)
            
            if success:
                return AgentResponse(
                    success=True,
                    result={"test_file": test_file_path},
                    files_created=[test_file_path]
                )
            else:
                return AgentResponse(
                    success=False,
                    error=f"Failed to create integration test file: {test_file_path}"
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating integration tests: {str(e)}"
            )
    
    def _generate_test_for_file(self, file_path: str, content: str, requirements: str) -> str:
        """Generate tests for a specific file."""
        prompt = f"""
        Create comprehensive pytest tests for the Python file: {file_path}
        
        Source code:
        {content}
        
        Requirements: {requirements}
        
        Generate tests that:
        1. Test all public functions and methods
        2. Include positive and negative test cases
        3. Test edge cases and error conditions
        4. Use appropriate fixtures and mocks
        5. Follow pytest best practices
        
        Provide complete test code with proper imports and setup.
        """
        
        return self.generate_response(prompt)
    
    def _generate_unit_tests(self, source_content: str, requirements: str, file_path: str) -> str:
        """Generate unit tests for specific functions or classes."""
        prompt = f"""
        Create unit tests for the file: {file_path}
        
        Source code:
        {source_content}
        
        Test requirements: {requirements}
        
        Create focused unit tests that:
        1. Test individual functions in isolation
        2. Use mocks for external dependencies
        3. Cover all code paths and edge cases
        4. Include parameterized tests where appropriate
        5. Follow AAA pattern (Arrange, Act, Assert)
        
        Provide complete pytest test code.
        """
        
        return self.generate_response(prompt)
    
    def _generate_integration_tests(self, requirements: str) -> str:
        """Generate integration tests for API endpoints and workflows."""
        prompt = f"""
        Create integration tests for the web application.
        
        Requirements: {requirements}
        
        Generate tests that:
        1. Test complete API workflows
        2. Test database interactions
        3. Test authentication and authorization
        4. Test error handling and edge cases
        5. Use test client for HTTP requests
        
        Include setup and teardown for test data.
        Provide complete pytest test code with fixtures.
        """
        
        return self.generate_response(prompt)
    
    def _generate_conftest(self) -> str:
        """Generate conftest.py with shared fixtures."""
        return '''"""
Shared test fixtures and configuration for pytest.
"""

import pytest
import tempfile
import os
from pathlib import Path

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)

@pytest.fixture
def sample_data():
    """Provide sample test data."""
    return {
        "test_user": {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        },
        "test_item": {
            "title": "Test Item",
            "description": "A test item for testing purposes",
            "price": 29.99
        }
    }

@pytest.fixture(scope="session")
def test_config():
    """Test configuration settings."""
    return {
        "TESTING": True,
        "SECRET_KEY": "test-secret-key",
        "DATABASE_URL": "sqlite:///:memory:",
        "WTF_CSRF_ENABLED": False
    }

@pytest.fixture
def mock_database():
    """Mock database for testing."""
    # This would be implemented based on the actual database setup
    pass

# Add more fixtures as needed for your specific application
'''
    
    def _generate_test_requirements(self) -> str:
        """Generate test requirements file."""
        return """# Testing dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1
requests-mock>=1.11.0
factory-boy>=3.3.0
faker>=19.6.2
"""
    
    def _parse_test_results(self, test_output: str) -> Dict[str, Any]:
        """Parse pytest output to extract test results."""
        results = {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0,
            "duration": 0.0,
            "coverage": None
        }
        
        lines = test_output.split('\n')
        
        for line in lines:
            # Parse test summary line
            if "passed" in line and ("failed" in line or "error" in line or "skipped" in line):
                # Extract numbers from summary line
                import re
                numbers = re.findall(r'\d+', line)
                if numbers:
                    if "passed" in line:
                        results["passed"] = int(numbers[0]) if numbers else 0
                    if "failed" in line:
                        failed_match = re.search(r'(\d+) failed', line)
                        if failed_match:
                            results["failed"] = int(failed_match.group(1))
                    if "error" in line:
                        error_match = re.search(r'(\d+) error', line)
                        if error_match:
                            results["errors"] = int(error_match.group(1))
                    if "skipped" in line:
                        skipped_match = re.search(r'(\d+) skipped', line)
                        if skipped_match:
                            results["skipped"] = int(skipped_match.group(1))
            
            # Parse duration
            if "seconds" in line and "=" in line:
                duration_match = re.search(r'([\d.]+) seconds', line)
                if duration_match:
                    results["duration"] = float(duration_match.group(1))
        
        results["total_tests"] = results["passed"] + results["failed"] + results["errors"] + results["skipped"]
        
        return results
    
    def _analyze_coverage(self, task: AgentTask) -> AgentResponse:
        """Analyze test coverage."""
        try:
            # Run tests with coverage
            result = subprocess.run(
                [sys.executable, "-m", "pytest", "--cov=.", "--cov-report=term-missing"],
                capture_output=True,
                text=True,
                cwd=self.project_path
            )
            
            coverage_output = result.stdout + result.stderr
            
            # Parse coverage information
            coverage_data = self._parse_coverage_output(coverage_output)
            
            return AgentResponse(
                success=True,
                result={
                    "coverage_output": coverage_output,
                    "coverage_data": coverage_data
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error analyzing coverage: {str(e)}"
            )
    
    def _parse_coverage_output(self, output: str) -> Dict[str, Any]:
        """Parse coverage output to extract metrics."""
        coverage_data = {
            "total_coverage": 0,
            "files": [],
            "missing_lines": []
        }
        
        lines = output.split('\n')
        
        for line in lines:
            if "TOTAL" in line and "%" in line:
                # Extract total coverage percentage
                import re
                percentage_match = re.search(r'(\d+)%', line)
                if percentage_match:
                    coverage_data["total_coverage"] = int(percentage_match.group(1))
        
        return coverage_data
    
    def _validate_code(self, task: AgentTask) -> AgentResponse:
        """Validate code quality and style."""
        try:
            file_path = task.description or "."
            
            validation_results = {
                "syntax_errors": [],
                "style_issues": [],
                "complexity_issues": []
            }
            
            # Check Python syntax
            if file_path.endswith('.py'):
                try:
                    with open(file_path, 'r') as f:
                        compile(f.read(), file_path, 'exec')
                except SyntaxError as e:
                    validation_results["syntax_errors"].append(str(e))
            
            return AgentResponse(
                success=True,
                result={"validation_results": validation_results}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error validating code: {str(e)}"
            )
    
    def _run_specific_test(self, task: AgentTask) -> AgentResponse:
        """Run a specific test file or test function."""
        try:
            test_target = task.description
            
            result = subprocess.run(
                [sys.executable, "-m", "pytest", test_target, "-v"],
                capture_output=True,
                text=True,
                cwd=self.project_path
            )
            
            test_output = result.stdout + result.stderr
            success = result.returncode == 0
            
            return AgentResponse(
                success=success,
                result={
                    "test_output": test_output,
                    "return_code": result.returncode,
                    "test_target": test_target
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error running specific test: {str(e)}"
            )
