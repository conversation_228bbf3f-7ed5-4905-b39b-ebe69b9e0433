"""
Tool manager for registering and executing tools.
"""

from typing import Dict, Any

from tools.base_tool import BaseTool
from tools.terminal_tool import TerminalTool
from tools.file_manager_tool import FileManagerTool

class ToolManager:
    """Manages the registration and execution of tools."""
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self._register_default_tools()
    
    def _register_default_tools(self) -> None:
        """Register the default tools."""
        self.register_tool("terminal", TerminalTool())
        self.register_tool("file_manager", FileManagerTool())
    
    def register_tool(self, name: str, tool: BaseTool) -> None:
        """Register a new tool."""
        self.tools[name] = tool
    
    def execute_tool(self, name: str, **kwargs) -> Dict[str, Any]:
        """Execute a tool by name."""
        if name not in self.tools:
            return {"error": f"Tool not found: {name}"}
        
        return self.tools[name].execute(**kwargs)
