"""
Tests for the WebDev Agent system.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from agents.base_agent import AgentTask, AgentResponse
from agents.planner_agent import PlannerAgent
from agents.developer_agent import DeveloperAgent
from agents.tester_agent import TesterAgent
from agents.terminal_agent import TerminalAgent
from memory.task_memory import TaskMemory
from utils.llm_manager import LL<PERSON>anager

@pytest.fixture
def temp_project_dir():
    """Create a temporary project directory."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)

@pytest.fixture
def mock_llm_manager():
    """Create a mock LLM manager."""
    mock_llm = Mock(spec=LLMManager)
    mock_llm.generate.return_value = "Mock LLM response"
    mock_llm.get_status.return_value = {
        "local_available": True,
        "cloud_available": False
    }
    return mock_llm

@pytest.fixture
def task_memory():
    """Create a task memory instance."""
    return TaskMemory(":memory:")

class TestBaseAgent:
    """Test the base agent functionality."""
    
    def test_agent_task_creation(self):
        """Test creating an agent task."""
        task = AgentTask(
            id="test_task_1",
            type="test_task",
            description="Test task description",
            priority=1
        )
        
        assert task.id == "test_task_1"
        assert task.type == "test_task"
        assert task.description == "Test task description"
        assert task.priority == 1
        assert task.status == "pending"
    
    def test_agent_response_creation(self):
        """Test creating an agent response."""
        response = AgentResponse(
            success=True,
            result={"key": "value"},
            files_created=["test.py"]
        )
        
        assert response.success is True
        assert response.result == {"key": "value"}
        assert response.files_created == ["test.py"]

class TestPlannerAgent:
    """Test the planner agent."""
    
    def test_planner_agent_initialization(self, task_memory, mock_llm_manager):
        """Test planner agent initialization."""
        agent = PlannerAgent(task_memory, mock_llm_manager)
        
        assert agent.name == "Planner"
        assert agent.role == "Task Planning and Project Analysis"
        assert "plan_project" in agent.get_supported_task_types()
    
    def test_planner_can_handle_planning_tasks(self, task_memory, mock_llm_manager):
        """Test that planner can handle planning tasks."""
        agent = PlannerAgent(task_memory, mock_llm_manager)
        
        task = AgentTask(
            id="plan_1",
            type="plan_project",
            description="Create a blog application"
        )
        
        assert agent.can_handle_task(task) is True
    
    def test_planner_cannot_handle_non_planning_tasks(self, task_memory, mock_llm_manager):
        """Test that planner cannot handle non-planning tasks."""
        agent = PlannerAgent(task_memory, mock_llm_manager)
        
        task = AgentTask(
            id="dev_1",
            type="create_file",
            description="Create app.py"
        )
        
        assert agent.can_handle_task(task) is False

class TestDeveloperAgent:
    """Test the developer agent."""
    
    def test_developer_agent_initialization(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test developer agent initialization."""
        agent = DeveloperAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        assert agent.name == "Developer"
        assert agent.role == "Code Generation and Implementation"
        assert "create_file" in agent.get_supported_task_types()
    
    def test_developer_can_handle_development_tasks(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test that developer can handle development tasks."""
        agent = DeveloperAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        task = AgentTask(
            id="dev_1",
            type="create_file",
            description="Create main application file: app.py"
        )
        
        assert agent.can_handle_task(task) is True
    
    @patch('agents.developer_agent.DeveloperAgent.generate_response')
    def test_create_project_structure(self, mock_generate, task_memory, mock_llm_manager, temp_project_dir):
        """Test creating project structure."""
        agent = DeveloperAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        task = AgentTask(
            id="struct_1",
            type="create_project_structure",
            description="Create Flask project structure"
        )
        
        response = agent.execute_task(task)
        
        assert response.success is True
        assert len(response.files_created) > 0

class TestTesterAgent:
    """Test the tester agent."""
    
    def test_tester_agent_initialization(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test tester agent initialization."""
        agent = TesterAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        assert agent.name == "Tester"
        assert agent.role == "Testing and Quality Assurance"
        assert "create_tests" in agent.get_supported_task_types()
    
    def test_tester_can_handle_testing_tasks(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test that tester can handle testing tasks."""
        agent = TesterAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        task = AgentTask(
            id="test_1",
            type="run_tests",
            description="Run all tests"
        )
        
        assert agent.can_handle_task(task) is True

class TestTerminalAgent:
    """Test the terminal agent."""
    
    def test_terminal_agent_initialization(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test terminal agent initialization."""
        agent = TerminalAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        assert agent.name == "Terminal"
        assert agent.role == "Command Execution and System Operations"
        assert "run_command" in agent.get_supported_task_types()
    
    def test_terminal_can_handle_command_tasks(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test that terminal can handle command tasks."""
        agent = TerminalAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        task = AgentTask(
            id="cmd_1",
            type="run_command",
            description="echo 'Hello World'"
        )
        
        assert agent.can_handle_task(task) is True
    
    def test_command_safety_check(self, task_memory, mock_llm_manager, temp_project_dir):
        """Test command safety checking."""
        agent = TerminalAgent(task_memory, mock_llm_manager, str(temp_project_dir))
        
        # Safe command
        assert agent._is_command_safe("echo 'hello'") is True
        assert agent._is_command_safe("python --version") is True
        
        # Dangerous command
        assert agent._is_command_safe("rm -rf /") is False
        assert agent._is_command_safe("del /f *") is False

class TestTaskMemory:
    """Test the task memory system."""
    
    def test_task_storage_and_retrieval(self, task_memory):
        """Test storing and retrieving tasks."""
        task = AgentTask(
            id="memory_test_1",
            type="test_task",
            description="Test task for memory",
            priority=1
        )
        
        # Store the task
        task_memory.store_task(task, "TestAgent", "test_project")
        
        # Retrieve the task
        retrieved_task = task_memory.get_task("memory_test_1")
        
        assert retrieved_task is not None
        assert retrieved_task.id == "memory_test_1"
        assert retrieved_task.type == "test_task"
        assert retrieved_task.description == "Test task for memory"
    
    def test_task_status_filtering(self, task_memory):
        """Test filtering tasks by status."""
        # Create tasks with different statuses
        task1 = AgentTask(id="task1", type="test", description="Task 1", status="pending")
        task2 = AgentTask(id="task2", type="test", description="Task 2", status="completed")
        task3 = AgentTask(id="task3", type="test", description="Task 3", status="pending")
        
        # Store tasks
        for task in [task1, task2, task3]:
            task_memory.store_task(task, "TestAgent", "test_project")
        
        # Get pending tasks
        pending_tasks = task_memory.get_tasks_by_status("pending")
        completed_tasks = task_memory.get_tasks_by_status("completed")
        
        assert len(pending_tasks) == 2
        assert len(completed_tasks) == 1
        assert all(task.status == "pending" for task in pending_tasks)
        assert all(task.status == "completed" for task in completed_tasks)

if __name__ == "__main__":
    pytest.main([__file__])
