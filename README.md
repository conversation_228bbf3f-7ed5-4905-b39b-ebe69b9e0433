# WebDev Agent 🤖

A **Cloud-Powered AI Web Development Agent** that autonomously generates full-stack web applications with comprehensive development tools. Uses multiple cloud LLM providers (Gemini, Groq, OpenRouter) with automatic fallback for maximum reliability.

## 🎯 Features

### **Multi-Agent Architecture**
- 🧠 **Planner Agent** – Breaks down app requests into tasks and subtasks
- 👨‍💻 **Developer Agent** – Writes Python code for frontend and backend
- 🧪 **Tester Agent** – Creates and runs comprehensive test suites
- 🖥️ **Terminal Agent** – Executes shell commands and manages dependencies
- 🛠️ **WebDev Tools Agent** – Code analysis, security scanning, performance testing

### **Framework Support**
- **Backend**: Flask, FastAPI with modern Python practices
- **Frontend**: Jinja2 templates, HTMX, Alpine.js, Bootstrap
- **Database**: SQLite, PostgreSQL, MongoDB with migrations
- **Testing**: pytest, unittest with automated test generation
- **Tools**: Code formatting, linting, security scanning, SEO analysis

### **Cloud-First LLM Integration**
- 🔵 **Google Gemini**: Fast and free for most tasks
- 🟢 **Groq**: Ultra-fast inference with Llama models
- 🟠 **OpenRouter**: Access to many free and paid models
- 🔴 **OpenAI/Anthropic**: Premium models as backup
- 🔄 **Automatic Fallback**: Seamless switching between providers
- 💾 **Persistent Memory**: SQLite-based task and project memory
- 📁 **Project Continuation**: Resume development intelligently

## 🚀 Quick Start

### **🎯 Super Quick Start (Recommended)**
```bash
# 1. Clone the repository
git clone <repository-url>
cd WebDevAgent

# 2. Install dependencies
pip install -r requirements.txt

# 3. Setup cloud LLM providers (interactive)
python setup_cloud.py

# 4. Launch web interface
python app.py web
```
This will configure cloud LLM providers and launch the web interface!

### **🌐 Web Interface (Primary Method)**
```bash
# Launch the interactive web dashboard
python app.py web

# Or with custom settings
python app.py web --host 0.0.0.0 --port 8080
```
**Features:**
- 📊 Real-time task monitoring and progress
- 💻 Integrated terminal with command execution
- 📁 File browser with syntax highlighting
- 🔍 Project analysis and management
- 📈 System logs and performance metrics

### **💻 CLI Interface (Alternative)**
```bash
# Use command-line interface
python app.py cli create "your project description"
python app.py cli --help
```

### **⚡ Direct Mode (Quick Testing)**
```bash
# Create project directly
python app.py "Create a blog application with user authentication"
```

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd WebDevAgent
pip install -r requirements.txt
```

2. **Configure Cloud LLM Providers**:

**🔵 Google Gemini (Recommended - Free)**
```bash
# Get API key from: https://makersuite.google.com/app/apikey
export GEMINI_API_KEY="your-gemini-api-key"
```

**🟢 Groq (Fast Inference)**
```bash
# Get API key from: https://console.groq.com/keys
export GROQ_API_KEY="your-groq-api-key"
```

**🟠 OpenRouter (Many Free Models)**
```bash
# Get API key from: https://openrouter.ai/keys
export OPENROUTER_API_KEY="your-openrouter-api-key"
```

**⚡ Quick Setup (Interactive)**
```bash
python setup_cloud.py  # Guides you through API key setup
```

### 🎮 Usage Examples

#### **Web Interface (Recommended)**
1. **Launch Dashboard**: `python app.py web`
2. **Open Browser**: Navigate to `http://localhost:5555`
3. **Create Project**: Enter description in the web form
4. **Monitor Progress**: Watch real-time logs and task execution
5. **Browse Files**: Use integrated file browser
6. **Run Commands**: Execute terminal commands directly

#### **CLI Interface**
```bash
# Create a blog application
python app.py cli create "Create a blog application with user authentication and post management"

# Continue existing project
python app.py cli continue-project "Add email notifications and user profiles"

# Analyze existing codebase
python app.py cli analyze --path ./my-project
```

#### **Direct Mode**
```bash
# Quick project creation
python app.py "Build a task management app with user registration and task CRUD operations"
```

## 📋 Usage Examples

### 1. **Simple Blog Application**
```bash
python main.py "Create a personal blog with posts, comments, and admin panel"
```

### 2. **E-commerce Store**
```bash
python main.py "Build an online store with products, shopping cart, and payment integration"
```

### 3. **Task Management System**
```bash
python main.py "Create a team task management system with projects, assignments, and deadlines"
```

### 4. **API-First Application**
```bash
python main.py "Build a REST API for a library management system with books, users, and borrowing"
```

## 🛠️ CLI Commands

### Project Management
```bash
# Create new application
python cli.py create "description" [--framework flask|fastapi] [--database sqlite|postgresql]

# Continue existing project
python cli.py continue-project "additional requirements" [--path /project/path]

# List all projects
python cli.py list-projects

# Analyze project structure
python cli.py analyze [--path /project/path]
```

### Testing & Quality
```bash
# Run tests
python cli.py test [--path /project/path]

# Check system status
python cli.py status
```

### Configuration
```bash
# Configure LLM and project settings
python cli.py config

# Initialize in current directory
python cli.py init
```

## 🏗️ Architecture

### **Agent Communication Flow**
```
User Request → Planner Agent → Task Breakdown
     ↓
Developer Agent ← Task Queue ← Terminal Agent
     ↓                           ↓
File Creation              Package Installation
     ↓                           ↓
Tester Agent ← Test Generation ← Code Validation
     ↓
Test Execution & Results
```

### **Memory System**
- **Task Memory**: Stores task execution history and results
- **Project Memory**: Maintains project state and file changes
- **Session Management**: Tracks development sessions and progress

### **File Structure**
```
WebDevAgent/
├── main.py                 # Main orchestrator
├── cli.py                  # Command-line interface
├── config.py               # Configuration management
├── agents/                 # Specialized agents
│   ├── planner_agent.py    # Task planning
│   ├── developer_agent.py  # Code generation
│   ├── tester_agent.py     # Testing & QA
│   └── terminal_agent.py   # Command execution
├── memory/                 # Persistent storage
│   ├── task_memory.py      # Task state management
│   └── project_memory.py   # Project context
├── utils/                  # Utility modules
│   ├── llm_manager.py      # LLM integration
│   ├── file_manager.py     # File operations
│   └── project_analyzer.py # Code analysis
└── tests/                  # Test suites
```

## ⚙️ Configuration

### **LLM Configuration**
```python
# Cloud LLM (Gemini)
GEMINI_API_KEY=your-api-key

# Cloud LLM (Groq)
GROQ_API_KEY=your-api-key

# Cloud LLM (OpenRouter)
OPENROUTER_API_KEY=your-api-key

# Cloud LLM (Google)
GOOGLE_API_KEY=your-api-key
```

### **Project Defaults**
```python
WEBDEV_BACKEND_FRAMEWORK=flask  # or fastapi
WEBDEV_DATABASE=sqlite          # or postgresql, mongodb
```

## 🧪 Testing

### **Run Agent Tests**
```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=. --cov-report=html

# Run specific test file
pytest tests/test_agents.py -v
```

### **Test Generated Applications**
```bash
# Test a generated app
cd generated-project/
python cli.py test

# Run specific tests
pytest tests/test_routes.py
```

## 🔧 Advanced Usage

### **Custom Agent Development**
```python
from agents.base_agent import BaseAgent, AgentTask, AgentResponse

class CustomAgent(BaseAgent):
    def get_supported_task_types(self):
        return ["custom_task"]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        # Custom implementation
        return AgentResponse(success=True)
```

### **Project Templates**
Create custom project templates in `templates/` directory:
```
templates/
├── flask_template/
│   ├── app.py
│   ├── models.py
│   └── requirements.txt
└── fastapi_template/
    ├── main.py
    └── requirements.txt
```

### **Memory Queries**
```python
from memory.task_memory import TaskMemory

memory = TaskMemory()
recent_tasks = memory.get_recent_tasks(limit=10)
failed_tasks = memory.get_failed_tasks()
stats = memory.get_statistics()
```

## 🐛 Troubleshooting

### **Common Issues**

1. **LLM Connection Failed**
   ```bash
   # Check API key
   echo $GEMINI_API_KEY
   ```

2. **Package Installation Errors**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   
   pip install -r requirements.txt
   ```

3. **Permission Errors**
   ```bash
   # Ensure write permissions
   chmod +w .
   
   # Check file ownership
   ls -la
   ```

### **Debug Mode**
```bash
# Enable verbose logging
python cli.py create "app description" --verbose

# Check system status
python cli.py status
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make changes and add tests
4. Run tests: `pytest tests/`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **CrewAI** for multi-agent framework inspiration

- **Flask/FastAPI** communities for excellent documentation
- **pytest** for comprehensive testing framework

---

**Built with ❤️ for developers who want AI-powered web development without the complexity.**
