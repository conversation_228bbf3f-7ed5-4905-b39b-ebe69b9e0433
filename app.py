#!/usr/bin/env python3
"""
WebDev Agent - Main Application Entry Point
Provides multiple interfaces: CLI, Web Dashboard, and Direct API access.
"""

import sys
import argparse
import threading
import time
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def launch_web_interface(host='localhost', port=5555, debug=False):
    """Launch the web interface."""
    try:
        from web_interface import WebDevAgentInterface
        
        print(f"🌐 Starting Web Interface...")
        print(f"📡 URL: http://{host}:{port}")
        print(f"🎯 Features: Real-time monitoring, task progress, file browser")
        print(f"💡 Tip: Use the web interface for interactive development")
        
        interface = WebDevAgentInterface(host=host, port=port)
        interface.run(debug=debug, open_browser=True)
        
    except ImportError as e:
        print(f"❌ Web interface dependencies missing: {e}")
        print("💡 Install with: pip install flask-socketio python-socketio eventlet")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start web interface: {e}")
        sys.exit(1)

def launch_cli():
    """Launch the CLI interface."""
    try:
        from cli import cli
        
        print(f"💻 Starting CLI Interface...")
        print(f"🎯 Use 'python app.py cli --help' for available commands")
        
        # Remove 'cli' from sys.argv so click doesn't see it
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        cli()
        
    except Exception as e:
        print(f"❌ Failed to start CLI: {e}")
        sys.exit(1)

def run_direct_command(description):
    """Run a direct command to create an application."""
    try:
        from main import WebDevAgent
        
        print(f"🤖 WebDev Agent - Direct Mode")
        print(f"📝 Description: {description}")
        print(f"📁 Working Directory: {Path.cwd()}")
        
        # Initialize agent
        agent = WebDevAgent()
        
        # Build application
        print(f"\n🚀 Building application...")
        result = agent.build_application(description)
        
        # Display results
        if result["success"]:
            print(f"\n✅ Application built successfully!")
            
            summary = result.get("summary", {})
            print(f"\n📊 Summary:")
            for key, value in summary.items():
                print(f"   {key.replace('_', ' ').title()}: {value}")
            
            files_created = result.get("files_created", [])
            if files_created:
                print(f"\n📄 Files Created:")
                for file_path in files_created:
                    print(f"   • {file_path}")
            
            print(f"\n🎯 Next Steps:")
            print(f"   1. Install dependencies: pip install -r requirements.txt")
            
            framework = summary.get('framework', 'flask')
            if framework == 'flask':
                print(f"   2. Run application: python app.py")
            elif framework == 'fastapi':
                print(f"   2. Run application: uvicorn main:app --reload")
            
            print(f"   3. Run tests: pytest tests/")
            
        else:
            print(f"\n❌ Application build failed!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Failed to run direct command: {e}")
        sys.exit(1)

def check_dependencies():
    """Check if all dependencies are installed."""
    missing_deps = []
    
    # Core dependencies
    try:
        import crewai
    except ImportError:
        missing_deps.append("crewai")
    
    
    
    try:
        import flask
    except ImportError:
        missing_deps.append("flask")
    
    # Web interface dependencies (optional)
    web_deps_missing = []
    try:
        import flask_socketio
    except ImportError:
        web_deps_missing.append("flask-socketio")
    
    if missing_deps:
        print(f"❌ Missing core dependencies: {', '.join(missing_deps)}")
        print(f"💡 Install with: pip install -r requirements.txt")
        return False
    
    if web_deps_missing:
        print(f"⚠️  Web interface dependencies missing: {', '.join(web_deps_missing)}")
        print(f"💡 Web interface will not be available")
        print(f"💡 Install with: pip install flask-socketio python-socketio eventlet")
    
    return True

def check_llm_setup():
    """Check LLM setup and provide guidance."""
    try:
        from utils.llm_manager import LLMManager
        from config import get_config
        
        config = get_config()
        llm_manager = LLMManager()
        status = llm_manager.get_status()
        
        print(f"🧠 Cloud LLM Status:")
        print(f"   Available Providers: {len(status['available_providers'])}")
        print(f"   Primary Provider: {status['cloud_provider']}")

        for provider, details in status.get('provider_details', {}).items():
            if details['available']:
                print(f"   ✅ {provider.title()}: {details['model']}")
            elif provider in ['gemini', 'groq', 'openrouter']:
                print(f"   ❌ {provider.title()}: Not configured")

        if not status['cloud_available']:
            print(f"\n⚠️  No cloud LLM providers available!")
            print(f"💡 Setup Options:")
            print(f"   1. Quick Setup: python setup_cloud.py")
            print(f"   2. Manual Setup:")
            print(f"      - Gemini: export GEMINI_API_KEY='your-key'")
            print(f"      - Groq: export GROQ_API_KEY='your-key'")
            print(f"      - OpenRouter: export OPENROUTER_API_KEY='your-key'")
            print(f"   3. Test Setup: python test_cloud_setup.py")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ LLM check failed: {e}")
        return False

def show_welcome():
    """Show welcome message and system status."""
    print("🤖 WebDev Agent - AI-Powered Web Development")
    print("=" * 50)
    print("🎯 Create full-stack web applications with AI")
    print("☁️  Cloud-powered with multiple LLM providers")
    print("🔧 Supports Flask, FastAPI, and modern web tech")
    print("🛠️  Comprehensive web development tools")
    print("=" * 50)
    
    # Check system status
    print("\nSystem Check:")
    deps_ok = check_dependencies()
    llm_ok = check_llm_setup()
    
    if deps_ok and llm_ok:
        print("\nSystem ready!")
    else:
        print("\nSystem needs configuration")
    
    print("\n🚀 Available Interfaces:")
    print("   • 🌐 Web Dashboard: python app.py web")
    print("   • 💻 CLI Commands: python app.py cli")
    print("   • ⚡ Direct Mode: python app.py 'create a blog app'")
    print("   • 🔧 Setup: python setup_cloud.py")
    print("   • 🧪 Test: python test_cloud_setup.py")
    print("   • ❓ Help: python app.py --help")


def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(
        description="WebDev Agent - AI-Powered Web Development Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python app.py web                                    # Launch web interface
  python app.py cli create "blog app"                  # Use CLI to create app
  python app.py "create a task management system"     # Direct mode
  python app.py --check                               # Check system status
        """
    )
    
    parser.add_argument(
        'command',
        nargs='?',
        help='Command to run: "web", "cli", or project description for direct mode'
    )
    
    parser.add_argument(
        '--host',
        default='localhost',
        help='Host for web interface (default: localhost)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=5555,
        help='Port for web interface (default: 5555)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug mode'
    )
    
    parser.add_argument(
        '--check',
        action='store_true',
        help='Check system status and dependencies'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='Don\'t open browser for web interface'
    )
    
    args = parser.parse_args()
    
    # Handle special flags
    if args.check:
        show_welcome()
        return
    
    # If no command provided, show welcome
    if not args.command:
        show_welcome()
        return
    
    command = args.command.lower()
    
    # Route to appropriate interface
    if command == 'web':
        launch_web_interface(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
    
    elif command == 'cli':
        launch_cli()
    
    else:
        # Treat as direct project description
        run_direct_command(args.command)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n👋 WebDev Agent stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
