# WebDev Agent - Cloud LLM Configuration
# Copy this file to .env and add your API keys

# Primary Providers (Free/Low-cost)
# =================================

# Google Gemini (Recommended - Free tier available)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Groq (Fast inference with Llama models)
# Get your API key from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# OpenRouter (Access to many free models)
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Backup Providers (Optional)
# ============================

# OpenAI (Premium models)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic (Claude models)
# Get your API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Configuration Options
# =====================

# Primary provider to use first (gemini, groq, openrouter, openai, anthropic)
WEBDEV_PRIMARY_PROVIDER=gemini

# Model selection (optional - uses defaults if not specified)
WEBDEV_GEMINI_MODEL=gemini-1.5-flash
WEBDEV_GROQ_MODEL=llama-3.1-70b-versatile
WEBDEV_OPENROUTER_MODEL=meta-llama/llama-3.1-8b-instruct:free

# Project defaults
WEBDEV_BACKEND_FRAMEWORK=flask
WEBDEV_DATABASE=sqlite

# Setup Instructions:
# ===================
# 1. Copy this file: cp .env.example .env
# 2. Add at least one API key above
# 3. Run: python setup_cloud.py (for interactive setup)
# 4. Test: python test_cloud_setup.py
# 5. Launch: python app.py web

# Free API Key Sources:
# ====================
# • Gemini: Free tier with generous limits
# • Groq: Free tier with fast inference
# • OpenRouter: Many free models available
# • You only need ONE API key to get started!
