"""
Task Memory system for storing and retrieving agent tasks and their results.
Uses SQLite for persistent storage with JSON serialization for complex data.
"""

import json
import sqlite3
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import asdict
from contextlib import contextmanager

from agents.base_agent import AgentTask

class TaskMemory:
    """Manages persistent storage of agent tasks and their execution history."""
    
    def __init__(self, db_path: str = "webdev_agent_memory.db"):
        self.db_path = Path(db_path)
        self.init_database()
    
    def init_database(self) -> None:
        """Initialize the SQLite database with required tables."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Tasks table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    description TEXT NOT NULL,
                    priority INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'pending',
                    created_at REAL NOT NULL,
                    started_at REAL,
                    completed_at REAL,
                    result TEXT,  -- JSON serialized
                    error TEXT,
                    dependencies TEXT,  -- JSON serialized list
                    agent_name TEXT,
                    project_id TEXT
                )
            """)
            
            # Agent performance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS agent_performance (
                    agent_name TEXT PRIMARY KEY,
                    tasks_completed INTEGER DEFAULT 0,
                    tasks_failed INTEGER DEFAULT 0,
                    average_task_time REAL DEFAULT 0.0,
                    last_active REAL
                )
            """)
            
            # Project sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_sessions (
                    session_id TEXT PRIMARY KEY,
                    project_path TEXT NOT NULL,
                    started_at REAL NOT NULL,
                    ended_at REAL,
                    status TEXT DEFAULT 'active',
                    metadata TEXT  -- JSON serialized
                )
            """)
            
            conn.commit()
    
    @contextmanager
    def get_connection(self):
        """Get a database connection with proper cleanup."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        try:
            yield conn
        finally:
            conn.close()
    
    def store_task(self, task: AgentTask, agent_name: str = None, project_id: str = None) -> None:
        """Store a task in the database."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO tasks (
                    id, type, description, priority, status, created_at,
                    started_at, completed_at, result, error, dependencies,
                    agent_name, project_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task.id,
                task.type,
                task.description,
                task.priority,
                task.status,
                task.created_at,
                task.started_at,
                task.completed_at,
                json.dumps(task.result) if task.result else None,
                task.error,
                json.dumps(task.dependencies) if task.dependencies else None,
                agent_name,
                project_id
            ))
            
            conn.commit()
    
    def get_task(self, task_id: str) -> Optional[AgentTask]:
        """Retrieve a task by ID."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_task(row)
            return None
    
    def get_tasks_by_status(self, status: str, limit: int = 100) -> List[AgentTask]:
        """Get tasks by status."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC LIMIT ?",
                (status, limit)
            )
            
            return [self._row_to_task(row) for row in cursor.fetchall()]
    
    def get_tasks_by_agent(self, agent_name: str, limit: int = 100) -> List[AgentTask]:
        """Get tasks executed by a specific agent."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM tasks WHERE agent_name = ? ORDER BY created_at DESC LIMIT ?",
                (agent_name, limit)
            )
            
            return [self._row_to_task(row) for row in cursor.fetchall()]
    
    def get_tasks_by_project(self, project_id: str, limit: int = 100) -> List[AgentTask]:
        """Get tasks for a specific project."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM tasks WHERE project_id = ? ORDER BY created_at DESC LIMIT ?",
                (project_id, limit)
            )
            
            return [self._row_to_task(row) for row in cursor.fetchall()]
    
    def get_recent_tasks(self, limit: int = 50) -> List[AgentTask]:
        """Get most recent tasks."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?",
                (limit,)
            )
            
            return [self._row_to_task(row) for row in cursor.fetchall()]
    
    def get_failed_tasks(self, limit: int = 50) -> List[AgentTask]:
        """Get failed tasks for analysis."""
        return self.get_tasks_by_status("failed", limit)
    
    def get_pending_tasks(self, limit: int = 50) -> List[AgentTask]:
        """Get pending tasks."""
        return self.get_tasks_by_status("pending", limit)
    
    def update_agent_performance(self, agent_name: str, tasks_completed: int, 
                                tasks_failed: int, average_task_time: float) -> None:
        """Update agent performance metrics."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO agent_performance (
                    agent_name, tasks_completed, tasks_failed, 
                    average_task_time, last_active
                ) VALUES (?, ?, ?, ?, ?)
            """, (agent_name, tasks_completed, tasks_failed, average_task_time, time.time()))
            
            conn.commit()
    
    def get_agent_performance(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get performance metrics for an agent."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT * FROM agent_performance WHERE agent_name = ?",
                (agent_name,)
            )
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            return None
    
    def start_project_session(self, session_id: str, project_path: str, 
                             metadata: Dict[str, Any] = None) -> None:
        """Start a new project session."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO project_sessions (
                    session_id, project_path, started_at, status, metadata
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                session_id,
                project_path,
                time.time(),
                "active",
                json.dumps(metadata) if metadata else None
            ))
            
            conn.commit()
    
    def end_project_session(self, session_id: str) -> None:
        """End a project session."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE project_sessions 
                SET ended_at = ?, status = 'completed'
                WHERE session_id = ?
            """, (time.time(), session_id))
            
            conn.commit()
    
    def get_project_sessions(self, project_path: str = None) -> List[Dict[str, Any]]:
        """Get project sessions, optionally filtered by path."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if project_path:
                cursor.execute(
                    "SELECT * FROM project_sessions WHERE project_path = ? ORDER BY started_at DESC",
                    (project_path,)
                )
            else:
                cursor.execute("SELECT * FROM project_sessions ORDER BY started_at DESC")
            
            return [dict(row) for row in cursor.fetchall()]
    
    def _row_to_task(self, row: sqlite3.Row) -> AgentTask:
        """Convert a database row to an AgentTask object."""
        return AgentTask(
            id=row['id'],
            type=row['type'],
            description=row['description'],
            priority=row['priority'],
            status=row['status'],
            created_at=row['created_at'],
            started_at=row['started_at'],
            completed_at=row['completed_at'],
            result=json.loads(row['result']) if row['result'] else None,
            error=row['error'],
            dependencies=json.loads(row['dependencies']) if row['dependencies'] else []
        )
    
    def clear_old_tasks(self, days_old: int = 30) -> int:
        """Clear tasks older than specified days."""
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "DELETE FROM tasks WHERE created_at < ?",
                (cutoff_time,)
            )
            deleted_count = cursor.rowcount
            conn.commit()
            
            return deleted_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get memory statistics."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Task statistics
            cursor.execute("SELECT COUNT(*) FROM tasks")
            total_tasks = cursor.fetchone()[0]
            
            cursor.execute("SELECT status, COUNT(*) FROM tasks GROUP BY status")
            status_counts = dict(cursor.fetchall())
            
            cursor.execute("SELECT agent_name, COUNT(*) FROM tasks GROUP BY agent_name")
            agent_task_counts = dict(cursor.fetchall())
            
            # Performance statistics
            cursor.execute("SELECT * FROM agent_performance")
            agent_performance = [dict(row) for row in cursor.fetchall()]
            
            return {
                "total_tasks": total_tasks,
                "status_counts": status_counts,
                "agent_task_counts": agent_task_counts,
                "agent_performance": agent_performance,
                "database_size": self.db_path.stat().st_size if self.db_path.exists() else 0
            }
