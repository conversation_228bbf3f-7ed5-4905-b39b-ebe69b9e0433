#!/usr/bin/env python3
"""
Complete Setup Script for WebDev Agent
Handles installation, configuration, and verification of the entire system.
"""

import sys
import subprocess
import platform
import os
import time
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🤖 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a step description."""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def run_command(command, description, check=True, capture_output=True):
    """Run a command with proper error handling."""
    print(f"🔄 {description}...")
    
    try:
        if isinstance(command, str):
            result = subprocess.run(
                command,
                shell=True,
                capture_output=capture_output,
                text=True,
                timeout=300  # 5 minutes timeout
            )
        else:
            result = subprocess.run(
                command,
                capture_output=capture_output,
                text=True,
                timeout=300
            )
        
        if check and result.returncode != 0:
            print(f"❌ Failed: {description}")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
        
        print(f"✅ Success: {description}")
        return True
        
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description}")
        return False
    except Exception as e:
        print(f"❌ Error: {description} - {e}")
        return False

def check_system_requirements():
    """Check system requirements."""
    print_step(1, "Checking System Requirements")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor}.{python_version.micro} (3.8+ required)")
        return False
    
    # Check operating system
    os_name = platform.system()
    print(f"✅ Operating System: {os_name}")
    
    # Check if pip is available
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      capture_output=True, check=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    
    
    return True

def install_python_dependencies():
    """Install Python dependencies."""
    print_step(2, "Installing Python Dependencies")
    
    # Upgrade pip first
    if not run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      "Installing requirements"):
        return False
    
    # Verify key imports
    try:
        import flask
        import flask_socketio
        print("✅ Flask and SocketIO installed")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    return True





def create_demo_structure():
    """Create demo project structure."""
    print_step(5, "Creating Demo Structure")
    
    # Create directories
    directories = [
        "demo_projects",
        "generated_projects", 
        "web_templates",
        "web_static"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Create a simple demo file
    demo_file = Path("demo_projects") / "example.md"
    demo_file.write_text("""# WebDev Agent Demo

This is a demo project created by WebDev Agent.

## Features
- AI-powered web development
- Real-time monitoring
- Local LLM support
- Multi-agent architecture

## Usage
1. Launch web interface: `python app.py web`
2. Create a project with natural language
3. Monitor progress in real-time
4. Browse and edit generated files
""")
    
    print("✅ Demo structure created")
    return True

def run_system_tests():
    """Run system tests to verify everything works."""
    print_step(6, "Running System Tests")
    
    try:
        # Run the test script
        result = subprocess.run([sys.executable, "test_web_interface.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ All system tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Tests timed out")
        return False
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

def show_completion_message():
    """Show completion message and next steps."""
    print_header("Setup Complete! 🎉")
    
    print("🚀 WebDev Agent is ready to use!")
    print("\n📖 Quick Start Options:")
    print("   1. 🌐 Web Interface (Recommended):")
    print("      python app.py web")
    print("      Then open: http://localhost:5555")
    
    print("\n   2. 💻 Command Line:")
    print("      python app.py cli create 'your project description'")
    
    print("\n   3. ⚡ Direct Mode:")
    print("      python app.py 'Create a blog application'")
    
    print("\n   4. 🧪 Quick Test:")
    print("      python start.py")
    
    print("\n📚 Documentation:")
    print("   • README.md - Complete documentation")
    print("   • example.py - Usage examples")
    print("   • python app.py --help - Command help")
    
    print("\n🔧 Configuration:")
    print("   • Config file: webdev_agent_config.json")
    print("   • Modify settings: python app.py cli config")
    
    print("\n💡 Tips:")
    print("   • Use descriptive project descriptions")
    print("   • Monitor progress in the web interface")
    print("   • Check generated files before running")
    print("   • Run tests after generation")

def main():
    """Main setup process."""
    print_header("WebDev Agent Setup")
    print("🎯 This script will set up everything you need to run WebDev Agent")
    print("⏱️  Estimated time: 5-15 minutes (depending on internet speed)")
    
    # Confirm before proceeding
    try:
        response = input("\nProceed with setup? (Y/n): ").strip().lower()
        if response in ['n', 'no']:
            print("👋 Setup cancelled")
            return
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        return
    
    # Run setup steps
    steps = [
        check_system_requirements,
        install_python_dependencies,
        create_demo_structure,
        run_system_tests
    ]
    
    failed_steps = []
    
    for i, step_func in enumerate(steps, 1):
        try:
            if not step_func():
                failed_steps.append(step_func.__name__)
        except KeyboardInterrupt:
            print("\n👋 Setup cancelled by user")
            return
        except Exception as e:
            print(f"❌ Step {i} failed with error: {e}")
            failed_steps.append(step_func.__name__)
    
    # Show results
    if not failed_steps:
        show_completion_message()
    else:
        print_header("Setup Completed with Issues")
        print("⚠️  Some steps failed:")
        for step in failed_steps:
            print(f"   • {step}")
        
        print("\n💡 You can still use WebDev Agent, but some features may not work.")
        print("   Check the error messages above and try to resolve them.")
        print("   You can re-run this setup script at any time.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Setup failed with unexpected error: {e}")
        sys.exit(1)
