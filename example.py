#!/usr/bin/env python3
"""
Example script demonstrating the WebDev Agent capabilities.
This script shows how to use the agent programmatically.
"""

import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from main import WebDevAgent
from config import get_config

def demo_simple_blog():
    """Demonstrate creating a simple blog application."""
    print("🚀 WebDev Agent Demo: Creating a Simple Blog Application")
    print("=" * 60)
    
    # Create a demo project directory
    demo_dir = Path("demo_blog_project")
    demo_dir.mkdir(exist_ok=True)
    
    # Initialize the agent
    print("Initializing WebDev Agent...")
    agent = WebDevAgent(str(demo_dir))
    
    # Define the project requirements
    requirements = """
    Create a simple blog application with the following features:
    1. User authentication (login/register)
    2. Create, read, update, delete blog posts
    3. Comment system for posts
    4. Admin panel for managing posts and users
    5. Responsive design with Bootstrap
    6. SQLite database for data storage
    7. Basic search functionality
    """
    
    print(f"Project Requirements:\n{requirements}")
    print("\nStarting application build...")
    
    # Build the application
    start_time = time.time()
    result = agent.build_application(requirements)
    end_time = time.time()
    
    # Display results
    print(f"\n{'='*60}")
    print("BUILD RESULTS")
    print(f"{'='*60}")
    
    if result["success"]:
        print("✅ Application built successfully!")
        print(f"⏱️  Build time: {end_time - start_time:.2f} seconds")
        print(f"📁 Project directory: {demo_dir}")
        
        # Show summary
        summary = result.get("summary", {})
        print(f"\n📊 Project Summary:")
        for key, value in summary.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        
        # Show created files
        files_created = result.get("files_created", [])
        if files_created:
            print(f"\n📄 Files Created ({len(files_created)}):")
            for file_path in files_created:
                print(f"   • {file_path}")
        
        # Show test results
        test_results = result.get("test_results", {})
        if test_results.get("success"):
            print(f"\n🧪 Tests: ✅ Passed")
        else:
            print(f"\n🧪 Tests: ❌ Failed - {test_results.get('error', 'Unknown error')}")
        
        # Show next steps
        print(f"\n🎯 Next Steps:")
        print(f"   1. cd {demo_dir}")
        print(f"   2. pip install -r requirements.txt")
        print(f"   3. python app.py")
        print(f"   4. Open http://localhost:5000 in your browser")
        
    else:
        print("❌ Application build failed!")
        print(f"Error: {result.get('error', 'Unknown error')}")
        
        if result.get("session_id"):
            print(f"Session ID: {result['session_id']}")
    
    return result

def demo_api_project():
    """Demonstrate creating a REST API project."""
    print("\n🚀 WebDev Agent Demo: Creating a REST API")
    print("=" * 60)
    
    # Create a demo project directory
    demo_dir = Path("demo_api_project")
    demo_dir.mkdir(exist_ok=True)
    
    # Initialize the agent
    agent = WebDevAgent(str(demo_dir))
    
    # Define the API requirements
    requirements = """
    Create a REST API for a task management system with:
    1. User authentication with JWT tokens
    2. CRUD operations for tasks (create, read, update, delete)
    3. Task categories and priorities
    4. User assignment to tasks
    5. Due date tracking and notifications
    6. API documentation with Swagger/OpenAPI
    7. PostgreSQL database integration
    8. Comprehensive test suite
    """
    
    print(f"API Requirements:\n{requirements}")
    
    # Build the API
    result = agent.build_application(requirements)
    
    if result["success"]:
        print("✅ REST API created successfully!")
        print(f"📁 Project directory: {demo_dir}")
        
        # Show API endpoints (this would be extracted from the generated code)
        print(f"\n🔗 API Endpoints (example):")
        endpoints = [
            "POST /auth/register - User registration",
            "POST /auth/login - User login",
            "GET /tasks - List all tasks",
            "POST /tasks - Create new task",
            "GET /tasks/{id} - Get specific task",
            "PUT /tasks/{id} - Update task",
            "DELETE /tasks/{id} - Delete task",
            "GET /docs - API documentation"
        ]
        
        for endpoint in endpoints:
            print(f"   • {endpoint}")
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. cd {demo_dir}")
        print(f"   2. pip install -r requirements.txt")
        print(f"   3. uvicorn main:app --reload")
        print(f"   4. Open http://localhost:8000/docs for API documentation")
        
    else:
        print("❌ API build failed!")
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    return result

def demo_project_analysis():
    """Demonstrate project analysis capabilities."""
    print("\n🔍 WebDev Agent Demo: Project Analysis")
    print("=" * 60)
    
    # Analyze the current WebDev Agent project
    agent = WebDevAgent(".")
    
    print("Analyzing the WebDev Agent project itself...")
    
    # Start a session for analysis
    session_id = agent.start_session("Analyze WebDev Agent project structure")
    
    # Analyze the project
    agent._analyze_existing_project()
    
    if agent.current_project:
        print("✅ Analysis complete!")
        
        project = agent.current_project
        print(f"\n📊 Project Analysis:")
        print(f"   Framework: {project.framework}")
        print(f"   Database: {project.database}")
        print(f"   Structure: {project.structure_type}")
        print(f"   Files Created: {len(project.files_created)}")
        print(f"   Files Modified: {len(project.files_modified)}")
        
        # Get detailed analysis
        analysis = agent.project_analyzer.analyze_project()
        print(f"\n📈 Detailed Metrics:")
        print(f"   Total Files: {len(analysis.files)}")
        print(f"   Total Lines: {analysis.total_lines}")
        print(f"   Complexity Score: {analysis.complexity_score}")
        
        if analysis.recommendations:
            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(analysis.recommendations[:3], 1):
                print(f"   {i}. {rec}")
    
    agent.end_session()

def demo_configuration():
    """Demonstrate configuration management."""
    print("\n⚙️ WebDev Agent Demo: Configuration")
    print("=" * 60)
    
    config = get_config()
    
    print("Current Configuration:")
    print(f"   LLM Provider: {'Local' if config.llm.use_local else 'Cloud'}")
    print(f"   Local Model: {config.llm.local_model}")
    print(f"   Cloud Provider: {config.llm.cloud_provider}")
    print(f"   Backend Framework: {config.project.backend_framework}")
    print(f"   Database: {config.project.database}")
    print(f"   Frontend Approach: {config.project.frontend_approach}")
    
    # Test LLM connection
    from utils.llm_manager import LLMManager
    
    try:
        llm_manager = LLMManager()
        status = llm_manager.get_status()
        
        print(f"\n🔗 LLM Status:")
        print(f"   Local Available: {'✅' if status['local_available'] else '❌'}")
        print(f"   Cloud Available: {'✅' if status['cloud_available'] else '❌'}")
        
    except Exception as e:
        print(f"\n❌ LLM Status Check Failed: {e}")

def main():
    """Run all demos."""
    print("🤖 WebDev Agent - Comprehensive Demo")
    print("=" * 60)
    
    # Check if we should run specific demo
    if len(sys.argv) > 1:
        demo_type = sys.argv[1].lower()
        
        if demo_type == "blog":
            demo_simple_blog()
        elif demo_type == "api":
            demo_api_project()
        elif demo_type == "analyze":
            demo_project_analysis()
        elif demo_type == "config":
            demo_configuration()
        else:
            print(f"Unknown demo type: {demo_type}")
            print("Available demos: blog, api, analyze, config")
    else:
        # Run all demos
        print("Running all demos...\n")
        
        # 1. Configuration demo
        demo_configuration()
        
        # 2. Project analysis demo
        demo_project_analysis()
        
        # 3. Simple blog demo
        demo_simple_blog()
        
        # 4. API demo (commented out to avoid long execution)
        # demo_api_project()
        
        print(f"\n🎉 All demos completed!")
        print(f"\nTo run individual demos:")
        print(f"   python example.py blog     # Create blog application")
        print(f"   python example.py api      # Create REST API")
        print(f"   python example.py analyze  # Analyze project")
        print(f"   python example.py config   # Show configuration")

if __name__ == "__main__":
    main()
