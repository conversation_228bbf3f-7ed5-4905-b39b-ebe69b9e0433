"""
Project Memory system for storing and retrieving project state and context.
Manages project information, file changes, and development history.
"""

import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import sqlite3

@dataclass
class ProjectState:
    """Represents the current state of a project."""
    project_id: str
    name: str
    path: str
    framework: str
    database: str
    structure_type: str
    created_at: float
    last_modified: float
    status: str  # active, paused, completed, archived
    description: str
    features: List[str]
    files_created: List[str]
    files_modified: List[str]
    dependencies: Dict[str, str]
    configuration: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class FileChange:
    """Represents a change made to a file."""
    id: str
    project_id: str
    file_path: str
    change_type: str  # created, modified, deleted
    timestamp: float
    agent_name: str
    task_id: str
    content_hash: str
    size_before: int
    size_after: int
    description: str

class ProjectMemory:
    """Manages persistent storage of project state and development history."""
    
    def __init__(self, db_path: str = "webdev_project_memory.db"):
        self.db_path = Path(db_path)
        self.init_database()
    
    def init_database(self) -> None:
        """Initialize the SQLite database with required tables."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Projects table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    project_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL,
                    framework TEXT,
                    database TEXT,
                    structure_type TEXT,
                    created_at REAL NOT NULL,
                    last_modified REAL NOT NULL,
                    status TEXT DEFAULT 'active',
                    description TEXT,
                    features TEXT,  -- JSON serialized
                    files_created TEXT,  -- JSON serialized
                    files_modified TEXT,  -- JSON serialized
                    dependencies TEXT,  -- JSON serialized
                    configuration TEXT,  -- JSON serialized
                    metadata TEXT  -- JSON serialized
                )
            """)
            
            # File changes table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS file_changes (
                    id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    change_type TEXT NOT NULL,
                    timestamp REAL NOT NULL,
                    agent_name TEXT,
                    task_id TEXT,
                    content_hash TEXT,
                    size_before INTEGER DEFAULT 0,
                    size_after INTEGER DEFAULT 0,
                    description TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (project_id)
                )
            """)
            
            # Project sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_sessions (
                    session_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    started_at REAL NOT NULL,
                    ended_at REAL,
                    session_type TEXT DEFAULT 'development',
                    goals TEXT,  -- JSON serialized
                    achievements TEXT,  -- JSON serialized
                    issues TEXT,  -- JSON serialized
                    notes TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects (project_id)
                )
            """)
            
            # Project milestones table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_milestones (
                    milestone_id TEXT PRIMARY KEY,
                    project_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    target_date REAL,
                    completed_date REAL,
                    status TEXT DEFAULT 'pending',
                    requirements TEXT,  -- JSON serialized
                    deliverables TEXT,  -- JSON serialized
                    FOREIGN KEY (project_id) REFERENCES projects (project_id)
                )
            """)
            
            conn.commit()
    
    @contextmanager
    def get_connection(self):
        """Get a database connection with proper cleanup."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def create_project(self, project_state: ProjectState) -> bool:
        """Create a new project in memory."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO projects (
                        project_id, name, path, framework, database, structure_type,
                        created_at, last_modified, status, description, features,
                        files_created, files_modified, dependencies, configuration, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    project_state.project_id,
                    project_state.name,
                    project_state.path,
                    project_state.framework,
                    project_state.database,
                    project_state.structure_type,
                    project_state.created_at,
                    project_state.last_modified,
                    project_state.status,
                    project_state.description,
                    json.dumps(project_state.features),
                    json.dumps(project_state.files_created),
                    json.dumps(project_state.files_modified),
                    json.dumps(project_state.dependencies),
                    json.dumps(project_state.configuration),
                    json.dumps(project_state.metadata)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error creating project: {e}")
            return False
    
    def get_project(self, project_id: str) -> Optional[ProjectState]:
        """Retrieve a project by ID."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM projects WHERE project_id = ?", (project_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_project_state(row)
                return None
                
        except Exception as e:
            print(f"Error retrieving project: {e}")
            return None
    
    def update_project(self, project_state: ProjectState) -> bool:
        """Update an existing project."""
        try:
            project_state.last_modified = time.time()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE projects SET
                        name = ?, path = ?, framework = ?, database = ?, structure_type = ?,
                        last_modified = ?, status = ?, description = ?, features = ?,
                        files_created = ?, files_modified = ?, dependencies = ?,
                        configuration = ?, metadata = ?
                    WHERE project_id = ?
                """, (
                    project_state.name,
                    project_state.path,
                    project_state.framework,
                    project_state.database,
                    project_state.structure_type,
                    project_state.last_modified,
                    project_state.status,
                    project_state.description,
                    json.dumps(project_state.features),
                    json.dumps(project_state.files_created),
                    json.dumps(project_state.files_modified),
                    json.dumps(project_state.dependencies),
                    json.dumps(project_state.configuration),
                    json.dumps(project_state.metadata),
                    project_state.project_id
                ))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"Error updating project: {e}")
            return False
    
    def list_projects(self, status: Optional[str] = None) -> List[ProjectState]:
        """List all projects, optionally filtered by status."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute(
                        "SELECT * FROM projects WHERE status = ? ORDER BY last_modified DESC",
                        (status,)
                    )
                else:
                    cursor.execute("SELECT * FROM projects ORDER BY last_modified DESC")
                
                return [self._row_to_project_state(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"Error listing projects: {e}")
            return []
    
    def record_file_change(self, file_change: FileChange) -> bool:
        """Record a file change."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO file_changes (
                        id, project_id, file_path, change_type, timestamp,
                        agent_name, task_id, content_hash, size_before, size_after, description
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    file_change.id,
                    file_change.project_id,
                    file_change.file_path,
                    file_change.change_type,
                    file_change.timestamp,
                    file_change.agent_name,
                    file_change.task_id,
                    file_change.content_hash,
                    file_change.size_before,
                    file_change.size_after,
                    file_change.description
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error recording file change: {e}")
            return False
    
    def get_file_changes(self, project_id: str, limit: int = 100) -> List[FileChange]:
        """Get file changes for a project."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM file_changes 
                    WHERE project_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """, (project_id, limit))
                
                return [self._row_to_file_change(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"Error retrieving file changes: {e}")
            return []
    
    def start_session(self, session_id: str, project_id: str, goals: List[str] = None) -> bool:
        """Start a new development session."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO project_sessions (
                        session_id, project_id, started_at, goals
                    ) VALUES (?, ?, ?, ?)
                """, (
                    session_id,
                    project_id,
                    time.time(),
                    json.dumps(goals or [])
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error starting session: {e}")
            return False
    
    def end_session(self, session_id: str, achievements: List[str] = None, 
                   issues: List[str] = None, notes: str = None) -> bool:
        """End a development session."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE project_sessions SET
                        ended_at = ?, achievements = ?, issues = ?, notes = ?
                    WHERE session_id = ?
                """, (
                    time.time(),
                    json.dumps(achievements or []),
                    json.dumps(issues or []),
                    notes,
                    session_id
                ))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            print(f"Error ending session: {e}")
            return False
    
    def get_project_by_path(self, project_path: str) -> Optional[ProjectState]:
        """Get project by its file system path."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM projects WHERE path = ?", (project_path,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_project_state(row)
                return None
                
        except Exception as e:
            print(f"Error retrieving project by path: {e}")
            return None
    
    def get_recent_projects(self, limit: int = 10) -> List[ProjectState]:
        """Get recently modified projects."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM projects 
                    ORDER BY last_modified DESC 
                    LIMIT ?
                """, (limit,))
                
                return [self._row_to_project_state(row) for row in cursor.fetchall()]
                
        except Exception as e:
            print(f"Error retrieving recent projects: {e}")
            return []
    
    def get_project_statistics(self, project_id: str) -> Dict[str, Any]:
        """Get statistics for a project."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # File change statistics
                cursor.execute("""
                    SELECT change_type, COUNT(*) as count
                    FROM file_changes 
                    WHERE project_id = ?
                    GROUP BY change_type
                """, (project_id,))
                
                change_stats = dict(cursor.fetchall())
                
                # Session statistics
                cursor.execute("""
                    SELECT COUNT(*) as session_count,
                           AVG(ended_at - started_at) as avg_session_duration
                    FROM project_sessions 
                    WHERE project_id = ? AND ended_at IS NOT NULL
                """, (project_id,))
                
                session_row = cursor.fetchone()
                session_stats = dict(session_row) if session_row else {}
                
                return {
                    "file_changes": change_stats,
                    "sessions": session_stats,
                    "total_files_created": change_stats.get("created", 0),
                    "total_files_modified": change_stats.get("modified", 0),
                    "total_files_deleted": change_stats.get("deleted", 0)
                }
                
        except Exception as e:
            print(f"Error retrieving project statistics: {e}")
            return {}
    
    def _row_to_project_state(self, row: sqlite3.Row) -> ProjectState:
        """Convert a database row to a ProjectState object."""
        return ProjectState(
            project_id=row['project_id'],
            name=row['name'],
            path=row['path'],
            framework=row['framework'],
            database=row['database'],
            structure_type=row['structure_type'],
            created_at=row['created_at'],
            last_modified=row['last_modified'],
            status=row['status'],
            description=row['description'],
            features=json.loads(row['features']) if row['features'] else [],
            files_created=json.loads(row['files_created']) if row['files_created'] else [],
            files_modified=json.loads(row['files_modified']) if row['files_modified'] else [],
            dependencies=json.loads(row['dependencies']) if row['dependencies'] else {},
            configuration=json.loads(row['configuration']) if row['configuration'] else {},
            metadata=json.loads(row['metadata']) if row['metadata'] else {}
        )
    
    def _row_to_file_change(self, row: sqlite3.Row) -> FileChange:
        """Convert a database row to a FileChange object."""
        return FileChange(
            id=row['id'],
            project_id=row['project_id'],
            file_path=row['file_path'],
            change_type=row['change_type'],
            timestamp=row['timestamp'],
            agent_name=row['agent_name'],
            task_id=row['task_id'],
            content_hash=row['content_hash'],
            size_before=row['size_before'],
            size_after=row['size_after'],
            description=row['description']
        )
    
    def cleanup_old_data(self, days_old: int = 90) -> Dict[str, int]:
        """Clean up old project data."""
        cutoff_time = time.time() - (days_old * 24 * 60 * 60)
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Clean up old file changes
                cursor.execute(
                    "DELETE FROM file_changes WHERE timestamp < ?",
                    (cutoff_time,)
                )
                file_changes_deleted = cursor.rowcount
                
                # Clean up old sessions
                cursor.execute(
                    "DELETE FROM project_sessions WHERE started_at < ?",
                    (cutoff_time,)
                )
                sessions_deleted = cursor.rowcount
                
                conn.commit()
                
                return {
                    "file_changes_deleted": file_changes_deleted,
                    "sessions_deleted": sessions_deleted
                }
                
        except Exception as e:
            print(f"Error cleaning up old data: {e}")
            return {"error": str(e)}
