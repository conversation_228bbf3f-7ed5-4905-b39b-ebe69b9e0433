"""
Terminal Agent for executing shell commands and system operations.
Handles command execution, package installation, and system interactions.
"""

import subprocess
import sys
import os
import shlex
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

from .base_agent import BaseAgent, AgentTask, AgentResponse
from memory.task_memory import TaskMemory
from utils.llm_manager import LLMManager

class TerminalAgent(BaseAgent):
    """Agent responsible for terminal operations and command execution."""
    
    def __init__(self, memory: TaskMemory, llm_manager: LLMManager, project_path: str = "."):
        super().__init__("Terminal", "Command Execution and System Operations", memory, llm_manager)
        self.project_path = Path(project_path).resolve()
        self.safe_commands = self._get_safe_commands()
        self.dangerous_commands = self._get_dangerous_commands()
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the terminal agent."""
        return """You are a System Administrator and DevOps Specialist.

Your role is to:
1. Execute safe shell commands for development tasks
2. Install and manage Python packages
3. Run development servers and tools
4. Manage project dependencies
5. Execute build and deployment scripts
6. Monitor system resources and processes

Safety Guidelines:
- Only execute commands that are safe for development environments
- Never run commands that could damage the system or delete important files
- Always validate commands before execution
- Use virtual environments for Python package management
- Provide clear output and error reporting
- Log all command executions for audit purposes

Allowed Operations:
- Package installation (pip, npm, etc.)
- Running development servers
- Database migrations and setup
- Code formatting and linting
- Test execution
- Git operations (read-only)
- File system operations within project directory
- Environment setup and configuration

Always prioritize safety and provide detailed feedback on command execution results."""
    
    def get_supported_task_types(self) -> List[str]:
        """Get list of task types this agent can handle."""
        return [
            "run_command",
            "install_packages",
            "setup_environment",
            "start_server",
            "run_migrations",
            "format_code",
            "lint_code",
            "git_operations",
            "create_virtualenv",
            "check_dependencies",
            "system_info"
        ]
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a terminal task."""
        if task.type == "run_command":
            return self._run_command(task)
        elif task.type == "install_packages":
            return self._install_packages(task)
        elif task.type == "setup_environment":
            return self._setup_environment(task)
        elif task.type == "start_server":
            return self._start_server(task)
        elif task.type == "run_migrations":
            return self._run_migrations(task)
        elif task.type == "format_code":
            return self._format_code(task)
        elif task.type == "lint_code":
            return self._lint_code(task)
        elif task.type == "create_virtualenv":
            return self._create_virtualenv(task)
        elif task.type == "check_dependencies":
            return self._check_dependencies(task)
        elif task.type == "system_info":
            return self._system_info(task)
        else:
            return AgentResponse(
                success=False,
                error=f"Unsupported task type: {task.type}"
            )
    
    def _run_command(self, task: AgentTask) -> AgentResponse:
        """Execute a shell command safely."""
        try:
            command = task.description.strip()
            
            # Validate command safety
            if not self._is_command_safe(command):
                return AgentResponse(
                    success=False,
                    error=f"Command not allowed for safety reasons: {command}"
                )
            
            # Execute the command
            result = self._execute_command(command)
            
            return AgentResponse(
                success=result["success"],
                result={
                    "command": command,
                    "stdout": result["stdout"],
                    "stderr": result["stderr"],
                    "return_code": result["return_code"],
                    "execution_time": result["execution_time"]
                },
                error=result["stderr"] if not result["success"] else None
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error executing command: {str(e)}"
            )
    
    def _install_packages(self, task: AgentTask) -> AgentResponse:
        """Install Python packages using pip."""
        try:
            packages = task.description.strip()
            
            # Handle different package specification formats
            if packages.startswith("requirements:"):
                # Install from requirements file
                req_file = packages.split(":", 1)[1].strip()
                command = f"{sys.executable} -m pip install -r {req_file}"
            else:
                # Install specific packages
                command = f"{sys.executable} -m pip install {packages}"
            
            result = self._execute_command(command)
            
            return AgentResponse(
                success=result["success"],
                result={
                    "packages": packages,
                    "command": command,
                    "output": result["stdout"],
                    "errors": result["stderr"]
                },
                error=result["stderr"] if not result["success"] else None
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error installing packages: {str(e)}"
            )
    
    def _setup_environment(self, task: AgentTask) -> AgentResponse:
        """Set up the development environment."""
        try:
            setup_steps = []
            results = []
            
            # Check if virtual environment exists
            venv_path = self.project_path / "venv"
            if not venv_path.exists():
                # Create virtual environment
                create_venv_result = self._execute_command(f"{sys.executable} -m venv venv")
                setup_steps.append("create_virtualenv")
                results.append(create_venv_result)
            
            # Install requirements if they exist
            req_file = self.project_path / "requirements.txt"
            if req_file.exists():
                install_cmd = f"{sys.executable} -m pip install -r requirements.txt"
                install_result = self._execute_command(install_cmd)
                setup_steps.append("install_requirements")
                results.append(install_result)
            
            # Set up pre-commit hooks if .pre-commit-config.yaml exists
            precommit_config = self.project_path / ".pre-commit-config.yaml"
            if precommit_config.exists():
                precommit_result = self._execute_command("pre-commit install")
                setup_steps.append("setup_precommit")
                results.append(precommit_result)
            
            all_successful = all(r["success"] for r in results)
            
            return AgentResponse(
                success=all_successful,
                result={
                    "setup_steps": setup_steps,
                    "results": results
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error setting up environment: {str(e)}"
            )
    
    def _start_server(self, task: AgentTask) -> AgentResponse:
        """Start the development server."""
        try:
            server_type = task.description.lower()
            
            if "flask" in server_type:
                # Start Flask development server
                command = f"{sys.executable} app.py"
            elif "fastapi" in server_type or "uvicorn" in server_type:
                # Start FastAPI with uvicorn
                command = f"{sys.executable} -m uvicorn main:app --reload --host 0.0.0.0 --port 8000"
            else:
                # Try to detect from files
                if (self.project_path / "app.py").exists():
                    command = f"{sys.executable} app.py"
                elif (self.project_path / "main.py").exists():
                    command = f"{sys.executable} -m uvicorn main:app --reload"
                else:
                    return AgentResponse(
                        success=False,
                        error="Could not determine how to start the server"
                    )
            
            # Note: This starts the server but doesn't wait for it to complete
            # In a real implementation, you might want to start it in the background
            return AgentResponse(
                success=True,
                result={
                    "command": command,
                    "message": f"Server start command prepared: {command}",
                    "note": "Execute this command to start the server"
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error starting server: {str(e)}"
            )
    
    def _format_code(self, task: AgentTask) -> AgentResponse:
        """Format code using black or other formatters."""
        try:
            target = task.description or "."
            
            # Try black first
            black_result = self._execute_command(f"{sys.executable} -m black {target}")
            
            if black_result["success"]:
                return AgentResponse(
                    success=True,
                    result={
                        "formatter": "black",
                        "target": target,
                        "output": black_result["stdout"]
                    }
                )
            else:
                # Fallback to autopep8
                autopep8_result = self._execute_command(f"{sys.executable} -m autopep8 --in-place --recursive {target}")
                
                return AgentResponse(
                    success=autopep8_result["success"],
                    result={
                        "formatter": "autopep8",
                        "target": target,
                        "output": autopep8_result["stdout"]
                    },
                    error=autopep8_result["stderr"] if not autopep8_result["success"] else None
                )
                
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error formatting code: {str(e)}"
            )
    
    def _lint_code(self, task: AgentTask) -> AgentResponse:
        """Lint code using flake8 or pylint."""
        try:
            target = task.description or "."
            
            # Try flake8 first
            flake8_result = self._execute_command(f"{sys.executable} -m flake8 {target}")
            
            return AgentResponse(
                success=True,  # flake8 returns non-zero for issues, but that's expected
                result={
                    "linter": "flake8",
                    "target": target,
                    "output": flake8_result["stdout"],
                    "issues": flake8_result["stderr"],
                    "return_code": flake8_result["return_code"]
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error linting code: {str(e)}"
            )
    
    def _create_virtualenv(self, task: AgentTask) -> AgentResponse:
        """Create a Python virtual environment."""
        try:
            venv_name = task.description or "venv"
            
            command = f"{sys.executable} -m venv {venv_name}"
            result = self._execute_command(command)
            
            return AgentResponse(
                success=result["success"],
                result={
                    "venv_name": venv_name,
                    "command": command,
                    "output": result["stdout"]
                },
                error=result["stderr"] if not result["success"] else None
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error creating virtual environment: {str(e)}"
            )
    
    def _check_dependencies(self, task: AgentTask) -> AgentResponse:
        """Check installed packages and their versions."""
        try:
            # Get list of installed packages
            pip_list_result = self._execute_command(f"{sys.executable} -m pip list")
            
            # Check for outdated packages
            pip_outdated_result = self._execute_command(f"{sys.executable} -m pip list --outdated")
            
            return AgentResponse(
                success=True,
                result={
                    "installed_packages": pip_list_result["stdout"],
                    "outdated_packages": pip_outdated_result["stdout"],
                    "pip_version": self._get_pip_version()
                }
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error checking dependencies: {str(e)}"
            )
    
    def _system_info(self, task: AgentTask) -> AgentResponse:
        """Get system information."""
        try:
            info = {
                "python_version": sys.version,
                "python_executable": sys.executable,
                "platform": sys.platform,
                "working_directory": str(self.project_path),
                "environment_variables": dict(os.environ)
            }
            
            return AgentResponse(
                success=True,
                result={"system_info": info}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error getting system info: {str(e)}"
            )
    
    def _execute_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Execute a command and return the result."""
        import time
        
        start_time = time.time()
        
        try:
            # Use shell=True on Windows, False on Unix-like systems
            use_shell = os.name == 'nt'
            
            result = subprocess.run(
                command if use_shell else shlex.split(command),
                shell=use_shell,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_path
            )
            
            execution_time = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode,
                "execution_time": execution_time
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "stdout": "",
                "stderr": f"Command timed out after {timeout} seconds",
                "return_code": -1,
                "execution_time": timeout
            }
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "return_code": -1,
                "execution_time": time.time() - start_time
            }
    
    def _is_command_safe(self, command: str) -> bool:
        """Check if a command is safe to execute."""
        command_lower = command.lower().strip()
        
        # Check for dangerous commands
        for dangerous in self.dangerous_commands:
            if dangerous in command_lower:
                return False
        
        # Check if command starts with a safe command
        first_word = command_lower.split()[0] if command_lower.split() else ""
        
        # Allow Python-related commands
        if first_word in ["python", "python3", sys.executable.lower()]:
            return True
        
        # Allow pip commands
        if "pip" in first_word:
            return True
        
        # Allow other safe commands
        return first_word in self.safe_commands
    
    def _get_safe_commands(self) -> List[str]:
        """Get list of safe commands that can be executed."""
        return [
            "python", "python3", "pip", "pip3",
            "pytest", "black", "flake8", "pylint", "mypy",
            "git", "ls", "dir", "pwd", "cd", "mkdir",
            "cat", "head", "tail", "grep", "find",
            "uvicorn", "gunicorn", "flask",
            "npm", "node", "yarn",
            "docker", "docker-compose",
            "curl", "wget", "ping",
            "echo", "which", "whereis"
        ]
    
    def _get_dangerous_commands(self) -> List[str]:
        """Get list of dangerous commands that should not be executed."""
        return [
            "rm -rf", "del /f", "format", "fdisk",
            "dd if=", "mkfs", "shutdown", "reboot",
            "halt", "poweroff", "init 0", "init 6",
            "chmod 777", "chown -R", "sudo rm",
            "killall", "pkill -9", "kill -9",
            "> /dev/", "| dd", ":/dev/",
            "eval", "exec", "system(",
            "import os; os.system"
        ]
    
    def _get_pip_version(self) -> str:
        """Get pip version."""
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "--version"],
                capture_output=True,
                text=True
            )
            return result.stdout.strip() if result.returncode == 0 else "unknown"
        except:
            return "unknown"
    
    def _run_migrations(self, task: AgentTask) -> AgentResponse:
        """Run database migrations."""
        try:
            migration_type = task.description.lower()
            
            if "flask" in migration_type or "alembic" in migration_type:
                # Flask-Migrate commands
                commands = [
                    f"{sys.executable} -m flask db init",
                    f"{sys.executable} -m flask db migrate",
                    f"{sys.executable} -m flask db upgrade"
                ]
            elif "django" in migration_type:
                # Django migrations
                commands = [
                    f"{sys.executable} manage.py makemigrations",
                    f"{sys.executable} manage.py migrate"
                ]
            else:
                # Generic database setup
                commands = [f"{sys.executable} -c \"from app import db; db.create_all()\""]
            
            results = []
            for command in commands:
                result = self._execute_command(command)
                results.append({
                    "command": command,
                    "success": result["success"],
                    "output": result["stdout"],
                    "error": result["stderr"]
                })
            
            all_successful = all(r["success"] for r in results)
            
            return AgentResponse(
                success=all_successful,
                result={"migration_results": results}
            )
            
        except Exception as e:
            return AgentResponse(
                success=False,
                error=f"Error running migrations: {str(e)}"
            )
