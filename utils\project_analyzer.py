"""
Project Analyzer for understanding existing project structures and resuming development.
Analyzes Python web projects to understand their architecture and current state.
"""

import ast
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict

@dataclass
class FileAnalysis:
    """Analysis result for a single file."""
    path: str
    type: str  # python, template, static, config, etc.
    size: int
    lines: int
    functions: List[str]
    classes: List[str]
    imports: List[str]
    routes: List[str]  # For Flask/FastAPI files
    models: List[str]  # Database models
    dependencies: List[str]
    complexity_score: int

@dataclass
class ProjectAnalysis:
    """Complete project analysis result."""
    project_path: str
    framework: str  # flask, fastapi, django, etc.
    structure_type: str  # monolithic, blueprints, modular
    database: str  # sqlite, postgresql, mongodb, etc.
    files: List[FileAnalysis]
    dependencies: Dict[str, str]
    entry_points: List[str]
    config_files: List[str]
    template_files: List[str]
    static_files: List[str]
    test_files: List[str]
    total_lines: int
    complexity_score: int
    recommendations: List[str]

class ProjectAnalyzer:
    """Analyzes Python web projects to understand their structure and state."""
    
    def __init__(self, project_path: str = "."):
        self.project_path = Path(project_path).resolve()
        self.analysis: Optional[ProjectAnalysis] = None
        
        # Framework detection patterns
        self.framework_patterns = {
            'flask': ['from flask import', 'Flask(__name__)', '@app.route'],
            'fastapi': ['from fastapi import', 'FastAPI()', '@app.get', '@app.post'],
            'django': ['from django', 'DJANGO_SETTINGS_MODULE', 'django.conf'],
            'starlette': ['from starlette', 'Starlette()', 'starlette.applications'],
            'tornado': ['import tornado', 'tornado.web', 'RequestHandler']
        }
        
        # Database detection patterns
        self.database_patterns = {
            'sqlite': ['sqlite3', 'SQLite', '.db'],
            'postgresql': ['psycopg2', 'postgresql', 'postgres'],
            'mysql': ['pymysql', 'mysql', 'MySQLdb'],
            'mongodb': ['pymongo', 'mongodb', 'mongo'],
            'sqlalchemy': ['sqlalchemy', 'SQLAlchemy', 'db.Model']
        }
    
    def analyze_project(self) -> ProjectAnalysis:
        """Perform complete project analysis."""
        if not self.project_path.exists():
            raise ValueError(f"Project path does not exist: {self.project_path}")
        
        # Initialize analysis
        self.analysis = ProjectAnalysis(
            project_path=str(self.project_path),
            framework="unknown",
            structure_type="unknown",
            database="unknown",
            files=[],
            dependencies={},
            entry_points=[],
            config_files=[],
            template_files=[],
            static_files=[],
            test_files=[],
            total_lines=0,
            complexity_score=0,
            recommendations=[]
        )
        
        # Analyze files
        self._analyze_files()
        
        # Detect framework and database
        self._detect_framework()
        self._detect_database()
        self._detect_structure_type()
        
        # Analyze dependencies
        self._analyze_dependencies()
        
        # Generate recommendations
        self._generate_recommendations()
        
        # Calculate metrics
        self._calculate_metrics()
        
        return self.analysis
    
    def _analyze_files(self) -> None:
        """Analyze all relevant files in the project."""
        # File type patterns
        python_patterns = ["*.py"]
        template_patterns = ["*.html", "*.jinja2", "*.j2"]
        static_patterns = ["*.css", "*.js", "*.scss", "*.less"]
        config_patterns = ["*.json", "*.yaml", "*.yml", "*.toml", "*.ini", "*.cfg"]
        
        # Analyze Python files
        for pattern in python_patterns:
            for file_path in self.project_path.rglob(pattern):
                if self._should_analyze_file(file_path):
                    analysis = self._analyze_python_file(file_path)
                    if analysis:
                        self.analysis.files.append(analysis)
        
        # Categorize other files
        for pattern in template_patterns:
            for file_path in self.project_path.rglob(pattern):
                if self._should_analyze_file(file_path):
                    self.analysis.template_files.append(str(file_path.relative_to(self.project_path)))
        
        for pattern in static_patterns:
            for file_path in self.project_path.rglob(pattern):
                if self._should_analyze_file(file_path):
                    self.analysis.static_files.append(str(file_path.relative_to(self.project_path)))
        
        for pattern in config_patterns:
            for file_path in self.project_path.rglob(pattern):
                if self._should_analyze_file(file_path):
                    self.analysis.config_files.append(str(file_path.relative_to(self.project_path)))
    
    def _should_analyze_file(self, file_path: Path) -> bool:
        """Check if a file should be analyzed."""
        # Skip hidden files and directories
        if any(part.startswith('.') for part in file_path.parts):
            return False
        
        # Skip common ignore patterns
        ignore_patterns = [
            '__pycache__', '*.pyc', '*.pyo', '*.pyd',
            'node_modules', 'venv', 'env', '.git',
            'dist', 'build', '*.egg-info'
        ]
        
        for pattern in ignore_patterns:
            if pattern in str(file_path):
                return False
        
        return True
    
    def _analyze_python_file(self, file_path: Path) -> Optional[FileAnalysis]:
        """Analyze a Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            try:
                tree = ast.parse(content)
            except SyntaxError:
                # File has syntax errors, still analyze what we can
                tree = None
            
            relative_path = str(file_path.relative_to(self.project_path))
            
            analysis = FileAnalysis(
                path=relative_path,
                type="python",
                size=len(content),
                lines=len(content.splitlines()),
                functions=[],
                classes=[],
                imports=[],
                routes=[],
                models=[],
                dependencies=[],
                complexity_score=0
            )
            
            # Analyze AST if available
            if tree:
                self._analyze_ast(tree, analysis)
            
            # Analyze content with regex patterns
            self._analyze_content_patterns(content, analysis)
            
            # Determine file type
            analysis.type = self._determine_file_type(analysis, content)
            
            # Calculate complexity
            analysis.complexity_score = self._calculate_file_complexity(analysis, content)
            
            return analysis
            
        except Exception as e:
            print(f"Error analyzing file {file_path}: {e}")
            return None
    
    def _analyze_ast(self, tree: ast.AST, analysis: FileAnalysis) -> None:
        """Analyze Python AST for functions, classes, and imports."""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                analysis.functions.append(node.name)
            elif isinstance(node, ast.ClassDef):
                analysis.classes.append(node.name)
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    analysis.imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    analysis.imports.append(node.module)
    
    def _analyze_content_patterns(self, content: str, analysis: FileAnalysis) -> None:
        """Analyze content using regex patterns."""
        # Flask routes
        flask_routes = re.findall(r'@app\.route\([\'"]([^\'"]+)[\'"]', content)
        analysis.routes.extend(flask_routes)
        
        # FastAPI routes
        fastapi_routes = re.findall(r'@app\.(get|post|put|delete|patch)\([\'"]([^\'"]+)[\'"]', content)
        analysis.routes.extend([route[1] for route in fastapi_routes])
        
        # SQLAlchemy models
        model_classes = re.findall(r'class\s+(\w+)\([^)]*Model[^)]*\):', content)
        analysis.models.extend(model_classes)
        
        # Database table definitions
        table_defs = re.findall(r'__tablename__\s*=\s*[\'"]([^\'"]+)[\'"]', content)
        analysis.models.extend(table_defs)
    
    def _determine_file_type(self, analysis: FileAnalysis, content: str) -> str:
        """Determine the specific type of Python file."""
        if analysis.routes:
            return "routes"
        elif analysis.models:
            return "models"
        elif "test" in analysis.path.lower() or any("test" in func for func in analysis.functions):
            return "test"
        elif "config" in analysis.path.lower():
            return "config"
        elif any("main" in func or "app" in func for func in analysis.functions):
            return "main"
        else:
            return "python"
    
    def _calculate_file_complexity(self, analysis: FileAnalysis, content: str) -> int:
        """Calculate a complexity score for the file."""
        score = 0
        score += len(analysis.functions) * 2
        score += len(analysis.classes) * 3
        score += len(analysis.routes) * 2
        score += len(analysis.models) * 3
        score += analysis.lines // 10
        
        # Add complexity for control structures
        control_structures = len(re.findall(r'\b(if|for|while|try|except|with)\b', content))
        score += control_structures
        
        return score
    
    def _detect_framework(self) -> None:
        """Detect the web framework being used."""
        framework_scores = {name: 0 for name in self.framework_patterns.keys()}
        
        for file_analysis in self.analysis.files:
            file_path = self.project_path / file_analysis.path
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for framework, patterns in self.framework_patterns.items():
                    for pattern in patterns:
                        if pattern in content:
                            framework_scores[framework] += 1
            except:
                continue
        
        # Determine the most likely framework
        if framework_scores:
            detected_framework = max(framework_scores, key=framework_scores.get)
            if framework_scores[detected_framework] > 0:
                self.analysis.framework = detected_framework
    
    def _detect_database(self) -> None:
        """Detect the database being used."""
        database_scores = {name: 0 for name in self.database_patterns.keys()}
        
        # Check imports and content
        for file_analysis in self.analysis.files:
            for import_name in file_analysis.imports:
                for database, patterns in self.database_patterns.items():
                    for pattern in patterns:
                        if pattern.lower() in import_name.lower():
                            database_scores[database] += 1
        
        # Check for database files
        for file_path in self.project_path.rglob("*.db"):
            database_scores['sqlite'] += 2
        
        # Determine the most likely database
        if database_scores:
            detected_database = max(database_scores, key=database_scores.get)
            if database_scores[detected_database] > 0:
                self.analysis.database = detected_database
    
    def _detect_structure_type(self) -> None:
        """Detect the project structure type."""
        # Check for blueprints (Flask)
        blueprint_files = [f for f in self.analysis.files if 'blueprint' in ' '.join(f.imports).lower()]
        
        # Check for modular structure
        has_models_dir = any('models/' in f.path for f in self.analysis.files)
        has_views_dir = any('views/' in f.path or 'routes/' in f.path for f in self.analysis.files)
        has_templates_dir = bool(self.analysis.template_files)
        
        if blueprint_files:
            self.analysis.structure_type = "blueprints"
        elif has_models_dir and has_views_dir:
            self.analysis.structure_type = "modular"
        elif len(self.analysis.files) <= 3:
            self.analysis.structure_type = "monolithic"
        else:
            self.analysis.structure_type = "mixed"
    
    def _analyze_dependencies(self) -> None:
        """Analyze project dependencies."""
        # Check requirements.txt
        req_file = self.project_path / "requirements.txt"
        if req_file.exists():
            try:
                with open(req_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '==' in line:
                                name, version = line.split('==', 1)
                                self.analysis.dependencies[name.strip()] = version.strip()
                            else:
                                self.analysis.dependencies[line] = "unknown"
            except:
                pass
        
        # Check pyproject.toml
        pyproject_file = self.project_path / "pyproject.toml"
        if pyproject_file.exists():
            # Basic TOML parsing for dependencies
            try:
                with open(pyproject_file, 'r') as f:
                    content = f.read()
                    # Simple regex to find dependencies
                    deps = re.findall(r'"([^"]+)"\s*=\s*"([^"]+)"', content)
                    for name, version in deps:
                        self.analysis.dependencies[name] = version
            except:
                pass
    
    def _generate_recommendations(self) -> None:
        """Generate recommendations for the project."""
        recommendations = []
        
        # Framework-specific recommendations
        if self.analysis.framework == "unknown":
            recommendations.append("Consider using a well-known framework like Flask or FastAPI")
        
        # Structure recommendations
        if self.analysis.structure_type == "monolithic" and len(self.analysis.files) > 5:
            recommendations.append("Consider refactoring into a modular structure with separate files for models, routes, and views")
        
        # Testing recommendations
        if not self.analysis.test_files:
            recommendations.append("Add unit tests to improve code reliability")
        
        # Configuration recommendations
        if not self.analysis.config_files:
            recommendations.append("Add configuration files for better environment management")
        
        # Database recommendations
        if self.analysis.database == "unknown":
            recommendations.append("Consider adding database integration for data persistence")
        
        # Documentation recommendations
        readme_exists = any("readme" in f.lower() for f in self.analysis.config_files)
        if not readme_exists:
            recommendations.append("Add a README.md file with project documentation")
        
        self.analysis.recommendations = recommendations
    
    def _calculate_metrics(self) -> None:
        """Calculate overall project metrics."""
        self.analysis.total_lines = sum(f.lines for f in self.analysis.files)
        self.analysis.complexity_score = sum(f.complexity_score for f in self.analysis.files)
        
        # Identify entry points
        for file_analysis in self.analysis.files:
            if (file_analysis.type == "main" or 
                "main" in file_analysis.path or 
                "app.py" in file_analysis.path or
                "run.py" in file_analysis.path):
                self.analysis.entry_points.append(file_analysis.path)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the project analysis."""
        if not self.analysis:
            return {}
        
        return {
            "framework": self.analysis.framework,
            "structure": self.analysis.structure_type,
            "database": self.analysis.database,
            "total_files": len(self.analysis.files),
            "total_lines": self.analysis.total_lines,
            "complexity_score": self.analysis.complexity_score,
            "has_tests": bool(self.analysis.test_files),
            "has_templates": bool(self.analysis.template_files),
            "dependencies_count": len(self.analysis.dependencies),
            "recommendations_count": len(self.analysis.recommendations)
        }
    
    def save_analysis(self, output_file: str = "project_analysis.json") -> bool:
        """Save the analysis to a JSON file."""
        if not self.analysis:
            return False
        
        try:
            output_path = self.project_path / output_file
            with open(output_path, 'w') as f:
                json.dump(asdict(self.analysis), f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving analysis: {e}")
            return False
