#!/usr/bin/env python3
"""
Quick Start Script for WebDev Agent
Automatically sets up and launches the web interface with proper configuration.
"""

import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("💡 Try running manually: pip install -r requirements.txt")
        return False





def create_demo_project():
    """Create a simple demo project to test the system."""
    print("🎯 Creating demo project...")
    
    demo_dir = Path("demo_project")
    demo_dir.mkdir(exist_ok=True)
    
    # Create a simple test file
    test_file = demo_dir / "test.py"
    test_file.write_text('''
def hello_world():
    """Simple test function."""
    return "Hello from WebDev Agent!"

if __name__ == "__main__":
    print(hello_world())
''')
    
    print(f"✅ Demo project created at: {demo_dir}")
    return True

def launch_web_interface():
    """Launch the web interface."""
    print("🚀 Launching WebDev Agent Web Interface...")
    print("📡 Starting server on http://localhost:5555")
    print("🌐 Opening browser...")
    
    try:
        # Import and run the web interface
        from web_interface import WebDevAgentInterface
        
        interface = WebDevAgentInterface(host='localhost', port=5555)
        interface.run(debug=False, open_browser=True)
        
    except Exception as e:
        print(f"❌ Failed to launch web interface: {e}")
        return False

def main():
    """Main startup sequence."""
    print("🤖 WebDev Agent - Quick Start")
    print("=" * 40)
    
    # Step 1: Check Python version
    if not check_python_version():
        return False
    
    # Step 2: Install dependencies
    if not install_dependencies():
        print("⚠️  Continuing without dependency installation...")
    
    # Step 5: Create demo project
    create_demo_project()
    
    # Step 6: Launch web interface
    print("\n🎉 Setup complete! Launching web interface...")
    print("\n📖 Quick Guide:")
    print("   1. Use the web interface to create projects")
    print("   2. Monitor real-time progress and logs")
    print("   3. Browse generated files and run commands")
    print("   4. Test with: 'Create a simple blog application'")
    
    time.sleep(2)
    launch_web_interface()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Startup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        sys.exit(1)
